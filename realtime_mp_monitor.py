#!/usr/bin/env python3
"""
MIT Control - 实时 MP 数据监控工具

该脚本通过监控控制器的标准输出来实时采集和绘制 mp_fused 与 mp_real 的对比数据。

使用方法:
1. 启动控制器并启用调试输出
2. 运行此脚本: python3 realtime_mp_monitor.py
3. 或者通过管道: ./mit_ctrl m s | python3 realtime_mp_monitor.py

作者: MIT Control Team
日期: 2025-09-26
"""

import sys
import re
import threading
import time
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
from collections import deque
from datetime import datetime
import argparse

class RealtimeMPMonitor:
    """实时MP数据监控器"""
    
    def __init__(self, max_samples=500, update_interval=100):
        self.max_samples = max_samples
        self.update_interval = update_interval
        
        # 数据存储
        self.timestamps = deque(maxlen=max_samples)
        self.mp_fused = deque(maxlen=max_samples)
        self.mp_real = deque(maxlen=max_samples)
        self.mp_torque_based = deque(maxlen=max_samples)
        self.confidence = deque(maxlen=max_samples)
        
        # 线程安全锁
        self.lock = threading.Lock()
        
        # 正则表达式模式
        self.mass_pattern = r'\[MassEst\] total\(Fz sens\)=([0-9.-]+)\s+mp=([0-9.-]+)\s+mp_tau=([0-9.-]+)\s+mp_fused=([0-9.-]+)\s+conf=([0-9.-]+)'
        
        # 时间基准
        self.start_time = time.time()
        
        # 统计信息
        self.total_samples = 0
        self.last_update_time = time.time()
        
        # 图表设置
        self.fig, self.axes = plt.subplots(2, 2, figsize=(14, 10))
        self.fig.suptitle('实时 MP 数据监控', fontsize=16, fontweight='bold')
        
        self.setup_plots()
        
    def setup_plots(self):
        """设置图表"""
        # 子图1: 时间序列对比
        self.ax1 = self.axes[0, 0]
        self.line_fused, = self.ax1.plot([], [], 'b-', label='mp_fused', linewidth=2)
        self.line_real, = self.ax1.plot([], [], 'r--', label='mp_real', linewidth=2)
        self.line_torque, = self.ax1.plot([], [], 'g:', label='mp_torque_based', linewidth=1.5)
        
        self.ax1.set_xlabel('时间 (s)')
        self.ax1.set_ylabel('质量 (kg)')
        self.ax1.set_title('负载质量估计对比')
        self.ax1.legend()
        self.ax1.grid(True, alpha=0.3)
        
        # 子图2: 误差
        self.ax2 = self.axes[0, 1]
        self.line_error, = self.ax2.plot([], [], 'purple', linewidth=2)
        self.ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        self.ax2.set_xlabel('时间 (s)')
        self.ax2.set_ylabel('误差 (kg)')
        self.ax2.set_title('估计误差 (mp_fused - mp_real)')
        self.ax2.grid(True, alpha=0.3)
        
        # 子图3: 置信度
        self.ax3 = self.axes[1, 0]
        self.line_conf, = self.ax3.plot([], [], 'orange', linewidth=2)
        
        self.ax3.set_xlabel('时间 (s)')
        self.ax3.set_ylabel('置信度')
        self.ax3.set_title('扭矩路径置信度')
        self.ax3.grid(True, alpha=0.3)
        self.ax3.set_ylim(0, 1.1)
        
        # 子图4: 统计信息文本
        self.ax4 = self.axes[1, 1]
        self.ax4.axis('off')
        self.stats_text = self.ax4.text(0.1, 0.9, '', transform=self.ax4.transAxes, 
                                       fontsize=10, verticalalignment='top',
                                       fontfamily='monospace',
                                       bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
    
    def add_data_point(self, mp_fused_val, mp_real_val, mp_torque_val, conf_val):
        """添加数据点"""
        with self.lock:
            current_time = time.time() - self.start_time
            
            self.timestamps.append(current_time)
            self.mp_fused.append(mp_fused_val)
            self.mp_real.append(mp_real_val)
            self.mp_torque_based.append(mp_torque_val)
            self.confidence.append(conf_val)
            
            self.total_samples += 1
    
    def parse_line(self, line):
        """解析输入行"""
        match = re.search(self.mass_pattern, line)
        if match:
            try:
                total_fz = float(match.group(1))
                mp = float(match.group(2))
                mp_tau = float(match.group(3))
                mp_fused_val = float(match.group(4))
                conf = float(match.group(5))
                
                # mp_real 是固定值 5.0
                mp_real_val = 5.0
                
                self.add_data_point(mp_fused_val, mp_real_val, mp_tau, conf)
                return True
            except ValueError:
                pass
        return False
    
    def update_plots(self, frame):
        """更新图表"""
        with self.lock:
            if len(self.timestamps) == 0:
                return self.line_fused, self.line_real, self.line_torque, self.line_error, self.line_conf
            
            # 转换为numpy数组
            t = np.array(self.timestamps)
            mp_f = np.array(self.mp_fused)
            mp_r = np.array(self.mp_real)
            mp_t = np.array(self.mp_torque_based)
            conf = np.array(self.confidence)
            
            # 更新时间序列图
            self.line_fused.set_data(t, mp_f)
            self.line_real.set_data(t, mp_r)
            self.line_torque.set_data(t, mp_t)
            
            # 更新误差图
            error = mp_f - mp_r
            self.line_error.set_data(t, error)
            
            # 更新置信度图
            self.line_conf.set_data(t, conf)
            
            # 自动调整坐标轴
            if len(t) > 1:
                # X轴范围
                x_min, x_max = t[0], t[-1]
                x_range = x_max - x_min
                if x_range > 0:
                    x_margin = x_range * 0.05
                    
                    self.ax1.set_xlim(x_min - x_margin, x_max + x_margin)
                    self.ax2.set_xlim(x_min - x_margin, x_max + x_margin)
                    self.ax3.set_xlim(x_min - x_margin, x_max + x_margin)
                
                # Y轴范围
                all_mp = np.concatenate([mp_f, mp_r, mp_t])
                mp_min, mp_max = np.min(all_mp), np.max(all_mp)
                mp_range = mp_max - mp_min
                if mp_range > 0:
                    mp_margin = mp_range * 0.1
                    self.ax1.set_ylim(mp_min - mp_margin, mp_max + mp_margin)
                
                error_range = np.max(np.abs(error))
                if error_range > 0:
                    self.ax2.set_ylim(-error_range * 1.1, error_range * 1.1)
            
            # 更新统计信息
            if len(error) > 0:
                mae = np.mean(np.abs(error))
                rmse = np.sqrt(np.mean(error**2))
                max_error = np.max(np.abs(error))
                
                current_time = time.time()
                data_rate = self.total_samples / (current_time - self.start_time) if current_time > self.start_time else 0
                
                stats_str = f"""实时统计:

数据点数: {len(t)}
数据速率: {data_rate:.1f} Hz
运行时间: {t[-1]:.1f} s

当前值:
mp_fused: {mp_f[-1]:.3f} kg
mp_real: {mp_r[-1]:.3f} kg
mp_torque: {mp_t[-1]:.3f} kg
置信度: {conf[-1]:.3f}

误差统计:
MAE: {mae:.4f} kg
RMSE: {rmse:.4f} kg
最大误差: {max_error:.4f} kg"""
                
                self.stats_text.set_text(stats_str)
        
        return self.line_fused, self.line_real, self.line_torque, self.line_error, self.line_conf
    
    def input_reader_thread(self):
        """输入读取线程"""
        print("开始监控输入数据...")
        print("等待 [MassEst] 调试输出...")
        
        try:
            for line in sys.stdin:
                line = line.strip()
                if self.parse_line(line):
                    # 可选：打印解析成功的信息
                    pass
                    
        except KeyboardInterrupt:
            print("\n停止数据采集")
        except Exception as e:
            print(f"输入读取错误: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        # 启动输入读取线程
        input_thread = threading.Thread(target=self.input_reader_thread, daemon=True)
        input_thread.start()
        
        # 启动动画
        ani = animation.FuncAnimation(self.fig, self.update_plots, 
                                    interval=self.update_interval, blit=False)
        
        try:
            plt.show()
        except KeyboardInterrupt:
            print("\n程序退出")

def main():
    parser = argparse.ArgumentParser(description='实时 MP 数据监控工具')
    parser.add_argument('--max-samples', type=int, default=500, 
                       help='最大数据点数 (默认: 500)')
    parser.add_argument('--update-interval', type=int, default=100, 
                       help='图表更新间隔 (ms, 默认: 100)')
    
    args = parser.parse_args()
    
    print("MIT Control - 实时 MP 数据监控工具")
    print("=" * 50)
    print("使用说明:")
    print("1. 确保控制器启用了调试输出 (enable_pid_debug = true)")
    print("2. 通过管道将控制器输出传递给此脚本:")
    print("   ./mit_ctrl m s | python3 realtime_mp_monitor.py")
    print("3. 或者先启动此脚本，然后在另一个终端启动控制器")
    print("4. 按 Ctrl+C 退出")
    print("=" * 50)
    
    monitor = RealtimeMPMonitor(max_samples=args.max_samples, 
                              update_interval=args.update_interval)
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
