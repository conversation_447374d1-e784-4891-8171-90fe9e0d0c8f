#!/usr/bin/env python3
"""
MIT Control - mp_fused vs mp_real 对比绘图工具

该脚本用于绘制扰动估计器中 mp_fused 和 mp_real 的对比图表。
支持实时数据采集和离线数据分析两种模式。

使用方法:
1. 实时模式: python3 plot_mp_comparison.py --realtime
2. 离线模式: python3 plot_mp_comparison.py --file data.csv
3. 从调试输出解析: python3 plot_mp_comparison.py --parse-debug debug_output.txt

作者: MIT Control Team
日期: 2025-09-26
"""

import argparse
import matplotlib.pyplot as plt
import numpy as np
import re
import time
import threading
from collections import deque
import csv
from datetime import datetime

class MPDataCollector:
    """MP数据收集器"""
    
    def __init__(self, max_samples=1000):
        self.max_samples = max_samples
        self.timestamps = deque(maxlen=max_samples)
        self.mp_fused = deque(maxlen=max_samples)
        self.mp_real = deque(maxlen=max_samples)
        self.mp_torque_based = deque(maxlen=max_samples)
        self.confidence = deque(maxlen=max_samples)
        self.lock = threading.Lock()
        
    def add_data(self, timestamp, mp_fused_val, mp_real_val, mp_torque_val=None, conf_val=None):
        """添加数据点"""
        with self.lock:
            self.timestamps.append(timestamp)
            self.mp_fused.append(mp_fused_val)
            self.mp_real.append(mp_real_val)
            if mp_torque_val is not None:
                self.mp_torque_based.append(mp_torque_val)
            if conf_val is not None:
                self.confidence.append(conf_val)
    
    def get_data_arrays(self):
        """获取数据数组"""
        with self.lock:
            return (np.array(self.timestamps), 
                   np.array(self.mp_fused), 
                   np.array(self.mp_real),
                   np.array(self.mp_torque_based) if self.mp_torque_based else None,
                   np.array(self.confidence) if self.confidence else None)
    
    def save_to_csv(self, filename=None):
        """保存数据到CSV文件"""
        if filename is None:
            filename = f"mp_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        with self.lock:
            with open(filename, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['timestamp', 'mp_fused', 'mp_real', 'mp_torque_based', 'confidence'])
                
                for i in range(len(self.timestamps)):
                    row = [self.timestamps[i], self.mp_fused[i], self.mp_real[i]]
                    if i < len(self.mp_torque_based):
                        row.append(self.mp_torque_based[i])
                    else:
                        row.append('')
                    if i < len(self.confidence):
                        row.append(self.confidence[i])
                    else:
                        row.append('')
                    writer.writerow(row)
        
        print(f"数据已保存到: {filename}")
        return filename

def parse_debug_output(file_path):
    """解析调试输出文件"""
    collector = MPDataCollector()
    
    # 正则表达式匹配调试输出
    mass_pattern = r'\[MassEst\] total\(Fz sens\)=([0-9.-]+)\s+mp=([0-9.-]+)\s+mp_tau=([0-9.-]+)\s+mp_fused=([0-9.-]+)\s+conf=([0-9.-]+)'
    
    try:
        with open(file_path, 'r') as f:
            timestamp = 0
            for line in f:
                match = re.search(mass_pattern, line)
                if match:
                    total_fz = float(match.group(1))
                    mp = float(match.group(2))
                    mp_tau = float(match.group(3))
                    mp_fused_val = float(match.group(4))
                    conf = float(match.group(5))
                    
                    # mp_real 是固定值 5.0，从代码中可以看到
                    mp_real_val = 5.0
                    
                    collector.add_data(timestamp, mp_fused_val, mp_real_val, mp_tau, conf)
                    timestamp += 0.002  # 假设2ms采样周期
        
        print(f"从调试输出解析了 {len(collector.timestamps)} 个数据点")
        return collector
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"解析文件时出错: {e}")
        return None

def load_csv_data(file_path):
    """从CSV文件加载数据"""
    collector = MPDataCollector()
    
    try:
        with open(file_path, 'r') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                timestamp = float(row['timestamp'])
                mp_fused_val = float(row['mp_fused'])
                mp_real_val = float(row['mp_real'])
                mp_torque_val = float(row['mp_torque_based']) if row['mp_torque_based'] else None
                conf_val = float(row['confidence']) if row['confidence'] else None
                
                collector.add_data(timestamp, mp_fused_val, mp_real_val, mp_torque_val, conf_val)
        
        print(f"从CSV文件加载了 {len(collector.timestamps)} 个数据点")
        return collector
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return None
    except Exception as e:
        print(f"加载CSV文件时出错: {e}")
        return None

def create_comparison_plot(collector, save_plot=True):
    """创建对比图表"""
    timestamps, mp_fused_data, mp_real_data, mp_torque_data, confidence_data = collector.get_data_arrays()
    
    if len(timestamps) == 0:
        print("警告: 没有数据可绘制")
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('MP Fused vs MP Real 对比分析', fontsize=16, fontweight='bold')
    
    # 子图1: mp_fused vs mp_real 时间序列
    ax1 = axes[0, 0]
    ax1.plot(timestamps, mp_fused_data, 'b-', label='mp_fused (估计值)', linewidth=2, alpha=0.8)
    ax1.plot(timestamps, mp_real_data, 'r--', label='mp_real (真实值)', linewidth=2, alpha=0.8)
    if mp_torque_data is not None and len(mp_torque_data) > 0:
        ax1.plot(timestamps[:len(mp_torque_data)], mp_torque_data, 'g:', 
                label='mp_torque_based', linewidth=1.5, alpha=0.7)
    
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('质量 (kg)')
    ax1.set_title('负载质量估计对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 误差分析
    ax2 = axes[0, 1]
    error = mp_fused_data - mp_real_data
    ax2.plot(timestamps, error, 'purple', linewidth=1.5, alpha=0.8)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.axhline(y=np.mean(error), color='red', linestyle='--', alpha=0.7, 
               label=f'平均误差: {np.mean(error):.3f} kg')
    ax2.fill_between(timestamps, error, alpha=0.3, color='purple')
    
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('误差 (kg)')
    ax2.set_title('估计误差 (mp_fused - mp_real)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 子图3: 置信度 (如果有数据)
    ax3 = axes[1, 0]
    if confidence_data is not None and len(confidence_data) > 0:
        ax3.plot(timestamps[:len(confidence_data)], confidence_data, 'orange', 
                linewidth=2, alpha=0.8)
        ax3.set_xlabel('时间 (s)')
        ax3.set_ylabel('置信度')
        ax3.set_title('扭矩路径置信度')
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1.1)
    else:
        ax3.text(0.5, 0.5, '无置信度数据', ha='center', va='center', 
                transform=ax3.transAxes, fontsize=12)
        ax3.set_title('扭矩路径置信度')
    
    # 子图4: 统计信息
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    # 计算统计指标
    mae = np.mean(np.abs(error))
    rmse = np.sqrt(np.mean(error**2))
    max_error = np.max(np.abs(error))
    std_error = np.std(error)
    
    stats_text = f"""统计指标:
    
平均绝对误差 (MAE): {mae:.4f} kg
均方根误差 (RMSE): {rmse:.4f} kg
最大绝对误差: {max_error:.4f} kg
误差标准差: {std_error:.4f} kg

数据范围:
mp_fused: [{np.min(mp_fused_data):.3f}, {np.max(mp_fused_data):.3f}] kg
mp_real: [{np.min(mp_real_data):.3f}, {np.max(mp_real_data):.3f}] kg

数据点数: {len(timestamps)}
时间跨度: {timestamps[-1] - timestamps[0]:.2f} s"""
    
    ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=11,
            verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    if save_plot:
        plot_filename = f"mp_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {plot_filename}")
    
    plt.show()
    
    return fig

def main():
    parser = argparse.ArgumentParser(description='MP Fused vs MP Real 对比绘图工具')
    parser.add_argument('--file', type=str, help='CSV数据文件路径')
    parser.add_argument('--parse-debug', type=str, help='调试输出文件路径')
    parser.add_argument('--realtime', action='store_true', help='实时数据采集模式 (暂未实现)')
    parser.add_argument('--no-save', action='store_true', help='不保存图表文件')
    
    args = parser.parse_args()
    
    collector = None
    
    if args.file:
        collector = load_csv_data(args.file)
    elif args.parse_debug:
        collector = parse_debug_output(args.parse_debug)
    elif args.realtime:
        print("实时数据采集模式暂未实现")
        print("请使用 --parse-debug 或 --file 选项")
        return
    else:
        # 生成示例数据用于演示
        print("未指定数据源，生成示例数据进行演示...")
        collector = MPDataCollector()
        
        # 生成示例数据
        t = np.linspace(0, 10, 500)
        mp_real_val = 5.0
        mp_fused_vals = mp_real_val + 0.5 * np.sin(0.5 * t) + 0.2 * np.random.randn(len(t))
        mp_torque_vals = mp_real_val + 0.3 * np.cos(0.3 * t) + 0.15 * np.random.randn(len(t))
        confidence_vals = 0.8 + 0.2 * np.sin(0.2 * t) + 0.05 * np.random.randn(len(t))
        confidence_vals = np.clip(confidence_vals, 0, 1)
        
        for i in range(len(t)):
            collector.add_data(t[i], mp_fused_vals[i], mp_real_val, 
                             mp_torque_vals[i], confidence_vals[i])
    
    if collector is not None:
        # 保存数据
        csv_filename = collector.save_to_csv()
        
        # 创建图表
        create_comparison_plot(collector, save_plot=not args.no_save)
        
        print(f"\n数据文件: {csv_filename}")
        print("绘图完成!")
    else:
        print("无法获取数据，程序退出")

if __name__ == "__main__":
    main()
