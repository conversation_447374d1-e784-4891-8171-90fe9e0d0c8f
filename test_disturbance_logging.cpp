/*
 * MIT Control - DisturbanceEstimator 数据记录功能测试
 * 
 * 该文件用于测试DisturbanceEstimator的数据记录功能是否正常编译和工作
 * 
 * 编译命令:
 * g++ -std=c++14 -I./src -I./src/user/MIT_Controller -I./src/common -I./src/third-party/eigen3 \
 *     test_disturbance_logging.cpp src/user/MIT_Controller/Controllers/DisturbanceEst/DisturbanceEstimator.cpp \
 *     -o test_disturbance_logging
 * 
 * 作者: MIT Control Team
 * 日期: 2025-09-26
 */

#include <iostream>
#include <memory>

// 模拟必要的类型定义（在实际项目中这些来自其他头文件）
template<typename T>
using Vec3 = Eigen::Matrix<T, 3, 1>;

template<typename T>
using Vec4 = Eigen::Matrix<T, 4, 1>;

template<typename T>
using Mat3 = Eigen::Matrix<T, 3, 3>;

template<typename T>
using Quat = Eigen::Quaternion<T>;

// 模拟MIT_UserParameters
struct MIT_UserParameters {
    bool enable_disturbance_logging = true;
    std::string disturbance_log_filename = "test_data.csv";
};

// 模拟ControlFSMData
template<typename T>
struct ControlFSMData {
    MIT_UserParameters* userParameters;
    // 其他成员在实际测试中不需要
};

// 包含DisturbanceEstimator头文件
#include "Controllers/DisturbanceEst/DisturbanceEstimator.h"

int main() {
    std::cout << "=== DisturbanceEstimator 数据记录功能测试 ===" << std::endl;
    
    try {
        // 创建参数
        MIT_UserParameters params;
        
        // 创建DisturbanceEstimator实例
        float dt = 0.002f;  // 2ms
        int iterations_between_mpc = 1;
        
        std::cout << "创建DisturbanceEstimator实例..." << std::endl;
        DisturbanceEstimator estimator(dt, iterations_between_mpc, &params);
        
        std::cout << "初始化..." << std::endl;
        estimator.initialize();
        
        std::cout << "启用数据记录..." << std::endl;
        estimator.enableDataLogging(true, "test_disturbance_data.csv");
        
        std::cout << "模拟数据记录..." << std::endl;
        // 模拟一些数据
        for (int i = 0; i < 100; ++i) {
            // 设置一些测试数据
            estimator.mp_fused = 5.0f + 0.1f * sin(i * 0.1f);
            estimator.mp_torque_based = 4.9f + 0.05f * cos(i * 0.15f);
            estimator.mp = 5.1f;
            estimator.torque_confidence = 0.8f + 0.1f * sin(i * 0.05f);
            estimator.total_contact_force = 140.0f + 10.0f * sin(i * 0.2f);
            
            // 设置加速度数据
            estimator.acc_world_3d << 0.1f * sin(i * 0.1f), 
                                      0.1f * cos(i * 0.1f), 
                                      -9.81f + 0.2f * sin(i * 0.05f);
            
            // 设置扰动数据
            estimator.disturbance << 0.05f * sin(i * 0.2f),
                                     0.03f * cos(i * 0.3f),
                                     0.02f * sin(i * 0.1f);
            
            estimator.d_est << 0.04f * sin(i * 0.25f),
                               0.02f * cos(i * 0.35f),
                               0.01f * sin(i * 0.15f);
            
            // 记录数据
            estimator.logData();
            
            // 模拟时间延迟
            if (i % 10 == 0) {
                std::cout << "已记录 " << (i + 1) << " 个数据点" << std::endl;
            }
        }
        
        std::cout << "停止数据记录..." << std::endl;
        estimator.enableDataLogging(false);
        
        std::cout << "导出数据..." << std::endl;
        estimator.saveDataToCSV("exported_test_data.csv");
        
        std::cout << "测试完成！" << std::endl;
        std::cout << "\n生成的文件:" << std::endl;
        std::cout << "- test_disturbance_data.csv" << std::endl;
        std::cout << "- exported_test_data.csv" << std::endl;
        std::cout << "\n使用以下命令查看数据:" << std::endl;
        std::cout << "python3 plot_disturbance_estimator.py --file exported_test_data.csv" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "未知错误" << std::endl;
        return 1;
    }
}
