#!/bin/bash

# MIT Control - DisturbanceEstimator 数据记录演示脚本
# 
# 该脚本演示了完整的数据记录和可视化工作流程
# 
# 作者: MIT Control Team
# 日期: 2025-09-26

echo "=== MIT Control DisturbanceEstimator 数据记录演示 ==="
echo

# 检查Python依赖
echo "检查Python依赖..."
python3 -c "import matplotlib, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: 缺少Python依赖包"
    echo "请安装: pip3 install matplotlib numpy"
    exit 1
fi
echo "✓ Python依赖检查通过"
echo

# 1. 生成演示数据并绘图
echo "1. 生成演示数据并创建图表..."
python3 plot_disturbance_estimator.py --demo --no-save
if [ $? -eq 0 ]; then
    echo "✓ 演示数据生成成功"
else
    echo "✗ 演示数据生成失败"
    exit 1
fi
echo

# 2. 显示统计信息
echo "2. 显示详细统计信息..."
python3 plot_disturbance_estimator.py --demo --stats-only
echo

# 3. 检查生成的文件
echo "3. 检查项目文件..."
echo "新增的文件:"
echo "  - plot_disturbance_estimator.py (数据可视化脚本)"
echo "  - enable_disturbance_logging_example.cpp (使用示例)"
echo "  - test_disturbance_logging.cpp (测试代码)"
echo "  - DISTURBANCE_ESTIMATOR_LOGGING_README.md (说明文档)"
echo

echo "修改的文件:"
echo "  - src/user/MIT_Controller/Controllers/DisturbanceEst/DisturbanceEstimator.h"
echo "  - src/user/MIT_Controller/Controllers/DisturbanceEst/DisturbanceEstimator.cpp"
echo

# 4. 显示使用说明
echo "4. 使用说明:"
echo
echo "在代码中启用数据记录:"
echo "  estimator.enableDataLogging(true);  // 自动文件名"
echo "  estimator.enableDataLogging(true, \"my_data.csv\");  // 指定文件名"
echo
echo "可视化数据:"
echo "  python3 plot_disturbance_estimator.py --file your_data.csv"
echo "  python3 plot_disturbance_estimator.py --demo  # 演示模式"
echo
echo "记录的数据包括:"
echo "  - mp_fused: 融合后的负载质量估计"
echo "  - mp_torque_based: 基于扭矩的负载质量估计"
echo "  - mp: 基于力传感器的负载质量估计"
echo "  - torque_confidence: 扭矩路径置信度"
echo "  - total_contact_force: 总接触力"
echo "  - acc_world_x/y/z: 世界系加速度"
echo "  - disturbance_x/y/z: 扰动估计"
echo "  - d_est_x/y/z: 扰动状态估计"
echo

echo "5. 集成到现有代码:"
echo
echo "在FSM状态中:"
echo "  // onEnter()中启用记录"
echo "  this->_data->_disturbanceEstimator->enableDataLogging(true);"
echo
echo "  // run()中正常运行 (数据自动记录)"
echo "  this->_data->_disturbanceEstimator->run(*this->_data);"
echo "  this->_data->_disturbanceEstimator->update(*this->_data);"
echo
echo "  // onExit()中停止记录"
echo "  this->_data->_disturbanceEstimator->enableDataLogging(false);"
echo

echo "=== 演示完成 ==="
echo
echo "更多信息请查看: DISTURBANCE_ESTIMATOR_LOGGING_README.md"
