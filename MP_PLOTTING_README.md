# MP Fused vs MP Real 绘图工具使用指南

## 概述

本工具集用于绘制和对比分析扰动估计器中的 `mp_fused`（融合质量估计）和 `mp_real`（真实负载质量）数据。提供了实时监控和离线分析两种模式。

## 文件说明

- `plot_mp_comparison.py` - 主绘图脚本，支持多种数据源
- `realtime_mp_monitor.py` - 实时数据监控工具
- `run_mp_monitor.sh` - 便捷启动脚本
- `MP_PLOTTING_README.md` - 本使用说明

## 快速开始

### 1. 安装依赖

```bash
# 安装 Python 依赖
pip3 install matplotlib numpy

# 或使用系统包管理器 (Ubuntu/Debian)
sudo apt install python3-matplotlib python3-numpy
```

### 2. 演示模式（推荐首次使用）

```bash
# 运行演示，查看绘图效果
./run_mp_monitor.sh demo
```

### 3. 实时监控模式

```bash
# 启动实时监控（需要控制器运行）
./run_mp_monitor.sh realtime
```

## 详细使用方法

### 方法一：使用便捷脚本（推荐）

```bash
# 查看帮助
./run_mp_monitor.sh help

# 演示模式 - 生成示例数据
./run_mp_monitor.sh demo

# 实时监控模式 - 监控运行中的控制器
./run_mp_monitor.sh realtime
```

### 方法二：直接使用 Python 脚本

#### 演示模式
```bash
# 生成示例数据并绘图
python3 plot_mp_comparison.py
```

#### 从调试输出文件绘图
```bash
# 首先保存控制器调试输出到文件
./mit_ctrl m s > debug_output.txt

# 然后解析并绘图
python3 plot_mp_comparison.py --parse-debug debug_output.txt
```

#### 从 CSV 文件绘图
```bash
# 如果已有 CSV 数据文件
python3 plot_mp_comparison.py --file mp_data.csv
```

#### 实时监控
```bash
# 方法1：通过管道
./mit_ctrl m s | python3 realtime_mp_monitor.py

# 方法2：分别启动
python3 realtime_mp_monitor.py  # 在一个终端
./mit_ctrl m s                  # 在另一个终端（需启用调试输出）
```

## 启用调试输出

为了获取 `mp_fused` 和 `mp_real` 的数据，需要在控制器中启用调试输出：

### 方法一：环境变量（推荐）
```bash
export ENABLE_PID_DEBUG=1
./mit_ctrl m s
```

### 方法二：修改代码
在 `DisturbanceEstimator.cpp` 中确保：
```cpp
enable_pid_debug = true;  // 在构造函数或初始化中设置
```

## 输出说明

### 图表内容

工具会生成包含4个子图的对比分析图：

1. **负载质量估计对比** - 显示 `mp_fused`、`mp_real` 和 `mp_torque_based` 的时间序列
2. **估计误差** - 显示 `mp_fused - mp_real` 的误差曲线
3. **扭矩路径置信度** - 显示融合算法的置信度变化
4. **统计信息** - 显示误差统计指标和数据概览

### 统计指标

- **MAE (平均绝对误差)** - 估计精度的主要指标
- **RMSE (均方根误差)** - 考虑误差分布的精度指标
- **最大绝对误差** - 最坏情况下的误差
- **误差标准差** - 误差的稳定性指标

### 文件输出

- **图表文件** - `mp_comparison_YYYYMMDD_HHMMSS.png`
- **数据文件** - `mp_data_YYYYMMDD_HHMMSS.csv`

## 调试输出格式

工具识别以下格式的调试输出：
```
[MassEst] total(Fz sens)=137.234  mp=0.123  mp_tau=4.567  mp_fused=4.890  conf=0.850
```

其中：
- `total(Fz sens)` - 力传感器总和
- `mp` - 力传感器路径质量估计
- `mp_tau` - 扭矩反演路径质量估计
- `mp_fused` - 融合后的质量估计
- `conf` - 扭矩路径置信度

## 故障排除

### 常见问题

1. **没有数据显示**
   - 检查是否启用了调试输出
   - 确认控制器正在运行
   - 检查调试输出格式是否正确

2. **图表不更新**
   - 检查数据采集频率
   - 确认输入数据流是否正常
   - 尝试重启监控工具

3. **依赖错误**
   ```bash
   # 重新安装依赖
   pip3 install --upgrade matplotlib numpy
   ```

4. **权限错误**
   ```bash
   # 给脚本添加执行权限
   chmod +x run_mp_monitor.sh
   chmod +x plot_mp_comparison.py
   chmod +x realtime_mp_monitor.py
   ```

### 调试技巧

1. **检查调试输出**
   ```bash
   # 查看控制器输出是否包含 [MassEst] 信息
   ./mit_ctrl m s | grep MassEst
   ```

2. **测试数据解析**
   ```bash
   # 使用示例输出测试解析
   echo "[MassEst] total(Fz sens)=137.234  mp=0.123  mp_tau=4.567  mp_fused=4.890  conf=0.850" | python3 realtime_mp_monitor.py
   ```

3. **查看详细错误**
   ```bash
   # 启用 Python 详细错误输出
   python3 -v plot_mp_comparison.py
   ```

## 高级用法

### 自定义参数

```bash
# 调整实时监控参数
python3 realtime_mp_monitor.py --max-samples 1000 --update-interval 50

# 不保存图表文件
python3 plot_mp_comparison.py --no-save
```

### 批量处理

```bash
# 处理多个调试输出文件
for file in debug_*.txt; do
    python3 plot_mp_comparison.py --parse-debug "$file"
done
```

### 数据导出

生成的 CSV 文件可以导入到其他分析工具：
- MATLAB: `readtable('mp_data.csv')`
- Excel: 直接打开 CSV 文件
- Python pandas: `pd.read_csv('mp_data.csv')`

## 技术支持

如遇到问题，请检查：
1. 控制器编译状态
2. Python 依赖安装
3. 调试输出是否启用
4. 数据格式是否正确

更多技术细节请参考源代码注释。
