# 四足机器人MPC扰动补偿集成方案（修正版）

## 概述

本文档描述了在四足机器人MPC控制器中集成扰动估计器的正确实现方案。基于用户澄清，扰动估计器输出的关键参数为：
- `mp_fused`：负载质量估计（kg）
- `d_est`：世界系角速度扰动项（rad/s²），等价于 I_world^(-1) * τ_mp

## 核心技术方案

### 1. 扰动补偿原理

**质量补偿**：
```cpp
total_mass = robot_mass + mp_fused
```

**角速度扰动补偿**：
在MPC预测模型中，角速度状态（6-8）添加扰动项：
```cpp
// 在QP线性项中添加扰动补偿
for(int i = 0; i < horizon; i++) {
  disturbance_compensation(13*i + 6) = d_est[0] * dt;
  disturbance_compensation(13*i + 7) = d_est[1] * dt; 
  disturbance_compensation(13*i + 8) = d_est[2] * dt;
}
qg += B_qp.transpose() * S * disturbance_compensation;
```

### 2. 数据流架构

```
DisturbanceEstimator → ConvexMPCLocomotion → MPC Solver
     ↓                        ↓                  ↓
mp_fused, d_est      setDisturbanceEstimator  扰动补偿优化
```

## 修改的文件和关键代码

### 1. convexMPC_interface.h
```cpp
struct update_data_t {
  // ... 原有字段 ...
  
  // 扰动补偿相关参数
  float payload_mass;          // 负载质量估计 mp_fused (kg)
  float angular_disturbance[3]; // 角速度扰动项 d_est (rad/s²，世界系)
  int enable_disturbance_compensation; // 是否启用扰动补偿
};

// 新增接口函数
EXTERNC void update_problem_data_floats_with_disturbance(
    float* p, float* v, float* q, float* w,
    float* r, float yaw, float* weights,
    float* state_trajectory, float alpha, int* gait,
    float payload_mass, float* angular_disturbance, 
    int enable_compensation);
```

### 2. SolverMPC.cpp
```cpp
// 质量补偿
if(update->enable_disturbance_compensation) {
  fpt total_mass = rs.m + update->payload_mass;
  ct_ss_mats(I_world, total_mass, rs.r_feet, rs.R_yaw, A_ct, B_ct_r, update->x_drag);
} else {
  ct_ss_mats(I_world, rs.m, rs.r_feet, rs.R_yaw, A_ct, B_ct_r, update->x_drag);
}

// 角速度扰动补偿
if(update->enable_disturbance_compensation) {
  Matrix<fpt,Dynamic,1> disturbance_compensation(13*setup->horizon);
  disturbance_compensation.setZero();
  
  for(s16 i = 0; i < setup->horizon; i++) {
    // 角速度扰动 (状态6-8)
    disturbance_compensation(13*i + 6) = update->angular_disturbance[0];
    disturbance_compensation(13*i + 7) = update->angular_disturbance[1]; 
    disturbance_compensation(13*i + 8) = update->angular_disturbance[2];
  }
  
  qg += B_qp.transpose()*S*disturbance_compensation;
}
```

### 3. ConvexMPCLocomotion.h/.cpp
```cpp
// 头文件
class DisturbanceEstimator; // 前向声明

class ConvexMPCLocomotion {
public:
  void setDisturbanceEstimator(DisturbanceEstimator* estimator) {
    _disturbance_estimator = estimator;
  }
  
private:
  DisturbanceEstimator* _disturbance_estimator = nullptr;
};

// 实现文件
if(_disturbance_estimator != nullptr) {
  float payload_mass = _disturbance_estimator->mp_fused;
  float angular_disturbance[3] = {
    _disturbance_estimator->d_est[0], 
    _disturbance_estimator->d_est[1], 
    _disturbance_estimator->d_est[2]
  };
  
  update_problem_data_floats_with_disturbance(
    p,v,q,w,r,yaw,weights,trajAll,alpha,mpcTable,
    payload_mass, angular_disturbance, 1);
} else {
  update_problem_data_floats(p,v,q,w,r,yaw,weights,trajAll,alpha,mpcTable);
}
```

### 4. SparseCMPC.h/.cpp
```cpp
// 头文件
template<typename T>
void setDisturbanceCompensation(T payload_mass, Vec3<T> angular_disturbance, bool enable = true) {
  _payload_mass = static_cast<double>(payload_mass);
  _angular_disturbance = angular_disturbance.template cast<double>();
  _enable_disturbance_compensation = enable;
}

private:
  Vec3<double> _angular_disturbance;
  double _payload_mass;
  bool _enable_disturbance_compensation;

// 实现文件
// 质量补偿
double total_mass = _mass + (_enable_disturbance_compensation ? _payload_mass : 0.0);
B.block(9,0,3,3) = Mat3<double>::Identity() / total_mass;

// 角速度扰动补偿
if(_enable_disturbance_compensation) {
  rhs[6] += _angular_disturbance[0] * _dtTrajectory[i];
  rhs[7] += _angular_disturbance[1] * _dtTrajectory[i];
  rhs[8] += _angular_disturbance[2] * _dtTrajectory[i];
}
```

### 5. DisturbanceEstimator.h
```cpp
public:
  // 扰动估计输出（从private移到public）
  Vec3<float> d_est; // 扰动状态（语义：实现中以角加速度扰动处理）
```

## 集成步骤

1. **建立连接**：在FSM_State_Locomotion.cpp中调用
   ```cpp
   cMPCOld->setDisturbanceEstimator(_disturbance_estimator);
   ```

2. **自动传递**：MPC控制器自动从扰动估计器获取mp_fused和d_est

3. **扰动补偿**：MPC求解器在优化过程中自动考虑负载质量和角速度扰动

## 预期效果

1. **负载适应性**：自动适应不同重量的负载，减少姿态偏差
2. **扰动抑制**：主动补偿角速度扰动，提高抗干扰能力  
3. **控制性能**：更精确的足端力分配，更稳定的运动表现

## 验证状态

✅ 编译成功，无错误
✅ 接口完整，支持双MPC求解器
✅ 参数传递正确
✅ 扰动补偿逻辑实现

## 下一步建议

1. **仿真测试**：在Gazebo中验证扰动补偿效果
2. **参数调优**：根据实际效果调整扰动估计器参数
3. **真机验证**：在真实机器人上测试负载补偿性能
4. **性能对比**：分析启用/禁用扰动补偿的控制差异
