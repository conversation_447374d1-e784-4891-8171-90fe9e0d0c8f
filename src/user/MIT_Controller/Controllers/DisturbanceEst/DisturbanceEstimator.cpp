#include "DisturbanceEstimator.h"
#include "Math/orientation_tools.h"

#include <eigen3/Eigen/Dense>
#include <qpOASES.hpp>

#include <memory>
#include <cmath>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <filesystem>

/* -------------------- 小工具：反对称与旋转 -------------------- */
template<typename T>
static inline Eigen::Matrix<T,3,3> skew3(const Eigen::Matrix<T,3,1>& v) {
    Eigen::Matrix<T,3,3> m;
    m <<    0, -v(2),  v(1),
        v(2),     0, -v(0),
        -v(1),  v(0),     0;
    return m;
}

// 将机体坐标向量旋到世界坐标：f_world = R_bw * f_body
static inline Eigen::Matrix<float,3,1> bodyToWorld(const Mat3<float>& R_wb, const Eigen::Matrix<float,3,1>& v_body) {
    // R_wb：world->body, 机体到世界的旋转是 R_bw = R_wb^T
    return R_wb.transpose() * v_body;
}

DisturbanceEstimator::DisturbanceEstimator(float _dt, int _iterations_between_mpc, MIT_UserParameters* parameters) {
    dt =_dt;
    iterations_between_mpc = _iterations_between_mpc;
    dtMPC = dt * iterations_between_mpc;
    _parameters = parameters;
}

void DisturbanceEstimator::initialize() {
    // 机体系转动惯量（对角）
    Eigen::Matrix<float,3,1> Id;
    Id << 1.58460467e-01f, 4.68645637e-01f, 5.24474661e-01f;
    I_body.setZero();
    I_body.diagonal() = Id;

    // 扰动估计增益/状态
    K = 5.0f * Eigen::Matrix<float,3,3>::Identity();
    mp = 0.0f;
    mp_torque_based = 0.0f;
    mp_fused = 0.0f;
    disturbance.setZero();
    d_est.setZero();

    for (int i = 0; i < 4; i++) {
        foot_force_tau_est[i].setZero();
        foot_force_des[i].setZero();
        pFoot[i].setZero(); // 注意：语义为世界系下相对COM的 r_world
    }

    gravity << 0.0f, 0.0f, -9.81f;

    // PID 初始化
    initializePIDControllers();

    // qpOASES：固定规模的 SQProblem （12变量、20约束）
    qp_ = std::make_unique<qpOASES::SQProblem>(kNV, kNC);
    qp_initialized_ = false;

    // 数据记录初始化
    enable_data_logging = false;
    logging_start_time = 0.0f;
    logging_counter = 0;

    // 可选：这里设置一次全局选项（也可在solveQP内每次设置）
    qpOASES::Options options;
    options.setToMPC();
    options.printLevel = qpOASES::PL_NONE;
    qp_->setOptions(options);
}

void DisturbanceEstimator::run(const ControlFSMData<float>& data) {
    // 读取估计结果
    const auto& estResult = data._stateEstimator->getResult();
    foot_force_est  = estResult.footForceEstimate;  // 通常是每足Fz估计（标量）
    contact_state   = estResult.contactEstimate;    // 0~1
    acc_world_3d    = estResult.aWorld;             // 请在工程内统一其是否含重力
    const Mat3<float>& R_wb = estResult.rBody;      // world->body

    // 由关节扭矩反演足端力（机体系）
    estimateFootForceFromTorque(data);

    // 统计接触
    int contact_count = 0;
    for (int i = 0; i < 4; i++) if (contact_state[i] > 0.1f) contact_count++;

    // 仅当四足接触时更新质量估计
    if (contact_count == 4) {
    // 扭矩反演得到的力（机体系）→ 世界系并求和
    Vec3<float> sum_F_world = Vec3<float>::Zero();
    float sum_Fz_tau_world  = 0.0f;

    for (int i = 0; i < 4; i++) {
        if (contact_state[i] > 0.1f) {
        F_world_tau_est[i] = bodyToWorld(R_wb, foot_force_tau_est[i]); // 脚受到的力（世界系）
        // 合力三维
        sum_F_world += F_world_tau_est[i];
        // 仅用于“垂直分量质量估计”的Z分量
        sum_Fz_tau_world += std::fabs(F_world_tau_est[i][2]);
        }
    }
    total_contact_force_tau_est = sum_F_world; // 三维合力（世界系）

    // 力传感器路径：把每足标量（通常为Fz）相加
    total_contact_force = 0.0f;
    for (int i = 0; i < 4; i++) {
        if (contact_state[i] > 0.1f) total_contact_force += foot_force_est[i];
    }

    // 采用acc_world_3d[2] 作为竖直加速度（工程内需统一其是否含重力）
    // 若 aWorld 含重力，上式估计相当于 mg ≈ Fz；若不含重力，相当于 m*az ≈ Fz。
    if ((acc_world_3d - gravity).norm() > 1e-3f) {
        // 路1：力传感器路径（标量Fz）
        float total_mass_original = total_contact_force / acc_world_3d[2];
        float mp_new_original = total_mass_original - body_mass;
        mp_new_original = std::fmax(-5.0f, std::fmin(20.0f, mp_new_original));

        // 路2：扭矩路径（用世界系Fz累加）
        float total_mass_torque = sum_Fz_tau_world / acc_world_3d[2];
        float mp_new_torque = total_mass_torque - body_mass;
        mp_new_torque = std::fmax(-5.0f, std::fmin(10.0f, mp_new_torque));

        // 一阶低通
        float alpha = 0.1f;
        mp              = alpha * mp_new_original + (1 - alpha) * mp;
        mp_torque_based = alpha * mp_new_torque  + (1 - alpha) * mp_torque_based;

        // 方向一致性与幅值合理性（用世界系三维合力）
        torque_confidence = 1.0f;
        Vec3<float> avg_dir = total_contact_force_tau_est.normalized();
        float vertical_alignment = std::fabs(avg_dir[2]); // 越接近1越垂直
        float force_mag = total_contact_force_tau_est.norm();
        float expected  = body_mass * 9.81f;

        if (vertical_alignment < 0.8f) torque_confidence *= 0.5f;
        if (std::fabs(force_mag - expected) > 0.5f * expected) torque_confidence *= 0.5f;

        float weight_torque   = 0.5f * torque_confidence;
        float weight_original = 1.0f - weight_torque;

        if (mp_new_torque < -5.0f || mp_new_torque > 30.0f) {
        weight_torque   *= 0.4f;
        weight_original  = 1.0f - weight_torque;
        }

        mp_fused = weight_original * mp + weight_torque * mp_torque_based;
        mp_fused = std::fmax(-5.0f, std::fmin(20.0f, mp_fused));
    }
    }

    // 可选：调试打印
    if (enable_pid_debug){
        static int counter = 0;
        counter++;
        if (counter % debug_print_frequency == 0) {
            std::cout << "[MassEst] total(Fz sens)=" << total_contact_force
            << "  mp=" << mp
            << "  mp_tau=" << mp_torque_based
            << "  mp_fused=" << mp_fused
            << "  conf=" << torque_confidence << std::endl;
            std::cout << "Acceleration (world): " << acc_world_3d.transpose() << std::endl;
        }
    }

    // 数据记录
    if (enable_data_logging) {
        logData();
    }
}
void DisturbanceEstimator::update(const ControlFSMData<float>& data) {
    _SetupCommand(data);
    updateTauEst(data);        // 更新扰动估计（世界系），并缓存 r_world 到 pFoot[]
    updatePIDControllers(data);

    // 导出到类成员（世界系）
    T_b = attitude_control_torque;
    F_b = position_control_force;

    // QP 力分配
    solveQP();
}

/* ============================================================= */
/*                       扭矩 -> 足端力估计                       */
/* ============================================================= */

void DisturbanceEstimator::estimateFootForceFromTorque(const ControlFSMData<float>& data) {
  if (!enable_torque_estimation) {
    for (int i = 0; i < 4; i++) {
      foot_force_tau_est[i].setZero();
      // 若未启用，则保持与原估计一致（这里假设 foot_force_est 是法向力标量，仅作为Fz占位）
      foot_force_tau_est[i][2] = foot_force_est[i];
    }
    return;
  }

  // 一阶低通的历史值
  static Vec3<float> foot_force_tau_est_prev[4] = {
    Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero(), Vec3<float>::Zero()
  };

  for (int leg = 0; leg < 4; leg++) {
    const Vec3<float>& tau_est = data._legController->datas[leg].tauEstimate;
    const Vec3<float>& q       = data._legController->datas[leg].q;
    const Vec3<float>& qd      = data._legController->datas[leg].qd;
    const Mat3<float>& J       = data._legController->datas[leg].J;

    Vec3<float> tau_gravity = enable_gravity_compensation ? computeGravityCompensation(q, leg, data) : Vec3<float>::Zero();
    Vec3<float> tau_inertia = enable_inertia_compensation ? computeInertiaCompensation(q, qd, leg, data) : Vec3<float>::Zero();

    Vec3<float> tau_contact = tau_est - tau_gravity - tau_inertia;

    // 对 J^T 做阻尼伪逆：(J^T)^+ = V * (Σ_damped)^(-1) * U^T
    Mat3<float> JT = J.transpose();
    Eigen::JacobiSVD<Mat3<float>> svd(JT, Eigen::ComputeFullU | Eigen::ComputeFullV);
    Mat3<float> Sigma_inv = Mat3<float>::Zero();
    auto s = svd.singularValues();
    for (int i = 0; i < 3; i++) {
      float denom = s(i) + kJtDamp; // 阻尼：1/(σ+ε)
      Sigma_inv(i,i) = (denom > 1e-8f) ? (1.0f / denom) : 0.0f;
    }
    Mat3<float> JT_pinv = svd.matrixV() * Sigma_inv * svd.matrixU().transpose();

    // F_body：机体系足端力（脚受到的力）
    Vec3<float> F_body = JT_pinv * tau_contact;

    // 低通滤波
    foot_force_tau_est[leg] = torque_filter_alpha * foot_force_tau_est_prev[leg]
                            + (1.0f - torque_filter_alpha) * F_body;
    foot_force_tau_est_prev[leg] = foot_force_tau_est[leg];
  }
}

/* ============================================================= */
/*                        重力/惯性补偿                           */
/* ============================================================= */

Vec3<float> DisturbanceEstimator::computeGravityCompensation(const Vec3<float>& q, int /*leg*/, const ControlFSMData<float>& data) {
  // 极简模型（示例）
  float m1 = 0.54f, m2 = 0.634f, m3 = 0.064f;
  float l1 = data._quadruped->_abadLinkLength;
  float l2 = data._quadruped->_hipLinkLength;
  float l3 = data._quadruped->_kneeLinkLength;

  float lc1 = 0.5f * l1, lc2 = 0.5f * l2, lc3 = 0.5f * l3;
  float g = 9.81f;

  float q1 = q[0], q2 = q[1], q3 = q[2];
  (void)q1; (void)lc1; // 本模型忽略 abad 的重力项

  Vec3<float> tau_gravity;
  tau_gravity[0] = 0.0f;
  tau_gravity[1] = g * (m2 * lc2 * std::cos(q2) + m3 * (l2 * std::cos(q2) + lc3 * std::cos(q2 + q3)));
  tau_gravity[2] = g *  m3 * lc3 * std::cos(q2 + q3);
  return tau_gravity;
}

Vec3<float> DisturbanceEstimator::computeInertiaCompensation(const Vec3<float>& q, const Vec3<float>& qd, int /*leg*/, const ControlFSMData<float>& data) {
  // 极简模型（示例）
  float m1 = 0.54f, m2 = 0.634f, m3 = 0.064f;
  float l1 = data._quadruped->_abadLinkLength;
  float l2 = data._quadruped->_hipLinkLength;
  float l3 = data._quadruped->_kneeLinkLength;

  float I1 = m1 * l1 * l1 / 12.0f;
  float I2 = m2 * l2 * l2 / 12.0f;
  float I3 = m3 * l3 * l3 / 12.0f;

  float q1 = q[0], q2 = q[1], q3 = q[2];
  float qd1 = qd[0], qd2 = qd[1], qd3 = qd[2];
  (void)q1; (void)q2; (void)q3;

  Vec3<float> tau_inertia;
  tau_inertia[0] = I1 * 0.1f * (qd2 * qd2 + qd3 * qd3);
  tau_inertia[1] = I2 * 0.2f * qd1 * qd3 * std::sin(q3);
  tau_inertia[2] = I3 * 0.1f * qd1 * qd2 * std::cos(q2);
  return tau_inertia;
}

/* ============================================================= */
/*                       指令 / PID 模块                          */
/* ============================================================= */

void DisturbanceEstimator::_SetupCommand(const ControlFSMData<float>& data) {
  if (data._quadruped->_robotType == RobotType::MINI_CHEETAH) {
    _body_height = 0.33f;
  } else if (data._quadruped->_robotType == RobotType::CHEETAH_3) {
    _body_height = 0.45f;
  } else {
    assert(false);
  }

  float x_vel_cmd, y_vel_cmd;
  float filter = 0.1f;
  const rc_control_settings* rc_cmd = data._desiredStateCommand->rcCommand;
  _yaw_turn_rate = -rc_cmd->omega_des[2];
  x_vel_cmd = rc_cmd->v_des[0];
  y_vel_cmd = rc_cmd->v_des[1] * 0.5f;
  _body_height += rc_cmd->height_variation * 0.08f;

  _x_vel_des = _x_vel_des * (1 - filter) + x_vel_cmd * filter;
  _y_vel_des = _y_vel_des * (1 - filter) + y_vel_cmd * filter;

  _yaw_des = data._stateEstimator->getResult().rpy[2] + dt * _yaw_turn_rate;
  _roll_des = 0.f;
  _pitch_des = 0.f;
}

void DisturbanceEstimator::initializePIDControllers() {
  // 姿态
  attitude_kp << 50.f, 50.f, 20.f;
  attitude_ki <<  5.f,  5.f,  2.f;
  attitude_kd <<  8.f,  8.f,  3.f;

  angular_velocity_kp << 10.f, 10.f,  5.f;
  angular_velocity_kd <<  2.f,  2.f,  1.f;

  // 位置
  position_kp << 100.f, 100.f, 150.f;
  position_ki <<  10.f,  10.f,  15.f;
  position_kd <<  20.f,  20.f,  25.f;

  velocity_kp << 50.f, 50.f, 60.f;
  velocity_kd <<  5.f,  5.f,  6.f;

  attitude_error_integral.setZero();
  attitude_error_prev.setZero();
  angular_velocity_error_prev.setZero();
  position_error_integral.setZero();
  position_error_prev.setZero();
  velocity_error_prev.setZero();

  attitude_control_torque.setZero();
  position_control_force.setZero();

  enable_attitude_pid = true;
  enable_position_pid = true;

  attitude_integral_limit = 10.f;
  position_integral_limit = 50.f;
}

Vec3<float> DisturbanceEstimator::computeAttitudeError(const Quat<float>& current_quat, const Quat<float>& desired_quat) {
  // q_error = q_desired * conj(q_current)
  Quat<float> current_quat_conj;
  current_quat_conj(0) = current_quat(0);
  current_quat_conj(1) = -current_quat(1);
  current_quat_conj(2) = -current_quat(2);
  current_quat_conj(3) = -current_quat(3);

  Quat<float> q_error;
  q_error(0) = desired_quat(0)*current_quat_conj(0) - desired_quat(1)*current_quat_conj(1)
             - desired_quat(2)*current_quat_conj(2) - desired_quat(3)*current_quat_conj(3);
  q_error(1) = desired_quat(0)*current_quat_conj(1) + desired_quat(1)*current_quat_conj(0)
             + desired_quat(2)*current_quat_conj(3) - desired_quat(3)*current_quat_conj(2);
  q_error(2) = desired_quat(0)*current_quat_conj(2) - desired_quat(1)*current_quat_conj(3)
             + desired_quat(2)*current_quat_conj(0) + desired_quat(3)*current_quat_conj(1);
  q_error(3) = desired_quat(0)*current_quat_conj(3) + desired_quat(1)*current_quat_conj(2)
             - desired_quat(2)*current_quat_conj(1) + desired_quat(3)*current_quat_conj(0);

  if (q_error(0) < 0) q_error = -q_error;

  Vec3<float> attitude_error;
  float norm_q = std::sqrt(q_error(1)*q_error(1) + q_error(2)*q_error(2) + q_error(3)*q_error(3));
  if (norm_q < 1e-6f) {
    attitude_error << 2.0f*q_error(1), 2.0f*q_error(2), 2.0f*q_error(3);
  } else {
    float angle = 2.0f * std::atan2(norm_q, std::fabs(q_error(0)));
    attitude_error << (angle / norm_q) * q_error(1),
                       (angle / norm_q) * q_error(2),
                       (angle / norm_q) * q_error(3);
  }
  return attitude_error;
}

Vec3<float> DisturbanceEstimator::computeAngularVelocityError(const Vec3<float>& current_omega, const Vec3<float>& desired_omega) {
  return desired_omega - current_omega;
}

Vec3<float> DisturbanceEstimator::computePositionError(const Vec3<float>& current_pos, const Vec3<float>& desired_pos) {
  return desired_pos - current_pos;
}

Vec3<float> DisturbanceEstimator::computeVelocityError(const Vec3<float>& current_vel, const Vec3<float>& desired_vel) {
  return desired_vel - current_vel;
}

Vec3<float> DisturbanceEstimator::attitudePIDControl(const Vec3<float>& attitude_error, const Vec3<float>& angular_velocity_error) {
  if (!enable_attitude_pid) return Vec3<float>::Zero();

  Vec3<float> proportional_term = attitude_kp.cwiseProduct(attitude_error);

  attitude_error_integral += attitude_error * dt;
  for (int i = 0; i < 3; i++) {
    if (attitude_error_integral[i] > attitude_integral_limit) attitude_error_integral[i] = attitude_integral_limit;
    else if (attitude_error_integral[i] < -attitude_integral_limit) attitude_error_integral[i] = -attitude_integral_limit;
  }
  Vec3<float> integral_term = attitude_ki.cwiseProduct(attitude_error_integral);
  integral_term = Vec3<float>::Zero(); // 不使用积分项
  Vec3<float> attitude_error_derivative = (attitude_error - attitude_error_prev) / dt;
  Vec3<float> derivative_term = attitude_kd.cwiseProduct(attitude_error_derivative);

  Vec3<float> angular_velocity_feedback = angular_velocity_kp.cwiseProduct(angular_velocity_error);
  Vec3<float> angular_velocity_derivative = (angular_velocity_error - angular_velocity_error_prev) / dt;
  Vec3<float> angular_velocity_damping = angular_velocity_kd.cwiseProduct(angular_velocity_derivative);

  Vec3<float> control_torque = proportional_term + integral_term + derivative_term
                             + angular_velocity_feedback + angular_velocity_damping;

  // 负载扰动补偿（量纲：I_world*d_est ~ 力矩）
  control_torque += I_world * d_est;
  // 调试输出
  if (enable_pid_debug) {
      static int counter = 0;
      counter++;
      if (counter % debug_print_frequency == 0) {
          std::cout << "=== Attitude PID Control ===\n";
          std::cout << "proportional_term: " << proportional_term.transpose() << std::endl;
          std::cout << "integral_term: " << integral_term.transpose() << std::endl;
          std::cout << "derivative_term: " << derivative_term.transpose() << std::endl;
          std::cout << "angular_velocity_feedback: " << angular_velocity_feedback.transpose() << std::endl;
          std::cout << "angular_velocity_damping: " << angular_velocity_damping.transpose() << std::endl;
          std::cout << "I_world * d_est: " << (I_world * d_est).transpose() << std::endl;
          std::cout << "control_torque: " << control_torque.transpose() << std::endl;
      }
  }

  attitude_error_prev = attitude_error;
  angular_velocity_error_prev = angular_velocity_error;
  return control_torque;
}

Vec3<float> DisturbanceEstimator::positionPIDControl(const Vec3<float>& position_error, const Vec3<float>& velocity_error) {
    if (!enable_position_pid) return Vec3<float>::Zero();

    const Vec3<float> a_lin = acc_world_3d + gravity;   // 静止时≈0向量
    const float m_total = body_mass + mp_fused; // 静止时≈总质量

    Vec3<float> proportional_term = position_kp.cwiseProduct(position_error);

    position_error_integral += position_error * dt;
    for (int i = 0; i < 3; i++) {
    if (position_error_integral[i] > position_integral_limit) position_error_integral[i] = position_integral_limit;
    else if (position_error_integral[i] < -position_integral_limit) position_error_integral[i] = -position_integral_limit;
    }
    Vec3<float> integral_term = position_ki.cwiseProduct(position_error_integral);
    integral_term = Vec3<float>::Zero(); // 不使用积分项
    Vec3<float> position_error_derivative = (position_error - position_error_prev) / dt;
    Vec3<float> derivative_term = position_kd.cwiseProduct(position_error_derivative);

    Vec3<float> velocity_feedback = velocity_kp.cwiseProduct(velocity_error);

    Vec3<float> velocity_derivative = (velocity_error - velocity_error_prev) / dt;
    Vec3<float> velocity_damping = velocity_kd.cwiseProduct(velocity_derivative);

    // 合外力（含加速度前馈/重力项的工程定义，需与项目内统一）
    Vec3<float> control_force = proportional_term + integral_term + derivative_term
                            + velocity_feedback + velocity_damping
                            + m_total * (-gravity)    // 只加一次 mg
                            - 6.5f * a_lin;        // 惯性前馈（aWorld 去重力后），因为机身只有6.5kg
    // Vec3<float> control_force = m_total * (-gravity)    // 只加一次 mg
    //                              + m_total * a_lin;        // 惯性前馈（aWorld 去重力后）
    // 调试输出
    if (enable_pid_debug) {
        static int counter = 0;
        counter++;
        if (counter % debug_print_frequency == 0) {
            printf("=== Position PID Control ===\n");
            printf("proportional_term: %f, %f, %f\n", proportional_term[0], proportional_term[1], proportional_term[2]);
            printf("integral_term: %f, %f, %f\n", integral_term[0], integral_term[1], integral_term[2]);
            printf("derivative_term: %f, %f, %f\n", derivative_term[0], derivative_term[1], derivative_term[2]);
            printf("velocity_feedback: %f, %f, %f\n", velocity_feedback[0], velocity_feedback[1], velocity_feedback[2]);
            printf("velocity_damping: %f, %f, %f\n", velocity_damping[0], velocity_damping[1], velocity_damping[2]);
            printf("m_total *alin: %f, %f, %f\n", m_total * a_lin[0], m_total * a_lin[1], m_total * a_lin[2]);
            printf("control_force: %f, %f, %f\n", control_force[0], control_force[1], control_force[2]);
            printf("m_total: %f, body_mass: %f, mp_fused: %f\n", m_total, body_mass, mp_fused);
        }
    }

    position_error_prev = position_error;
    velocity_error_prev = velocity_error;
    return control_force;
}

void DisturbanceEstimator::setPIDGains(const Vec3<float>& att_kp, const Vec3<float>& att_ki, const Vec3<float>& att_kd,
                                       const Vec3<float>& pos_kp, const Vec3<float>& pos_ki, const Vec3<float>& pos_kd) {
  attitude_kp = att_kp; attitude_ki = att_ki; attitude_kd = att_kd;
  position_kp = pos_kp; position_ki = pos_ki; position_kd = pos_kd;
}
 
void DisturbanceEstimator::updatePIDControllers(const ControlFSMData<float>& data) {
  const auto& estResult = data._stateEstimator->getResult();

  Vec3<float> current_position = estResult.position;        // 世界系
  Vec3<float> current_velocity = estResult.vWorld;          // 世界系
  Quat<float> current_orientation = estResult.orientation;  // wxyz
  Vec3<float> current_angular_velocity = estResult.omegaBody; // 机体系角速度

  // 期望
  Vec3<float> desired_position, desired_velocity, desired_rpy, desired_angular_velocity;
  desired_position[0] = current_position[0] + dt * _x_vel_des;
  desired_position[1] = current_position[1] + dt * _y_vel_des;
  desired_position[2] = _body_height;

  desired_velocity << _x_vel_des, _y_vel_des, 0.0f;
  desired_angular_velocity << 0.0f, 0.0f, _yaw_turn_rate;

  desired_rpy << 0.0f, 0.0f, _yaw_des;
  Quat<float> desired_orientation = ori::rpyToQuat(desired_rpy);

  // 误差
  Vec3<float> position_error         = computePositionError(current_position, desired_position);
  Vec3<float> velocity_error         = computeVelocityError(current_velocity, desired_velocity);
  Vec3<float> attitude_error         = computeAttitudeError(current_orientation, desired_orientation);
  Vec3<float> angular_velocity_error = computeAngularVelocityError(current_angular_velocity, desired_angular_velocity);

  // 控制
  if (enable_position_pid)  position_control_force  = positionPIDControl(position_error, velocity_error);
  if (enable_attitude_pid)  attitude_control_torque = attitudePIDControl(attitude_error, angular_velocity_error);

  // 可选调试
  // printPIDDebugInfo();
}

void DisturbanceEstimator::printPIDDebugInfo() {
  if (!enable_pid_debug) return;
  debug_counter++;
  if (debug_counter >= debug_print_frequency) {
    debug_counter = 0;
    std::cout << "=== PID Controller Debug ===\n";
    std::cout << "Att Torque: " << attitude_control_torque.transpose() << "\n";
    std::cout << "Pos  Force: " << position_control_force.transpose()   << "\n";
  }
}

void DisturbanceEstimator::enablePIDDebug(bool enable) {
  enable_pid_debug = enable;
  debug_counter = 0;
}

void DisturbanceEstimator::resetPIDIntegrals() {
  attitude_error_integral.setZero();
  position_error_integral.setZero();
  std::cout << "PID integral terms have been reset.\n";
}

/* ============================================================= */
/*                      扰动估计（世界系）                        */
/* ============================================================= */

void DisturbanceEstimator::updateTauEst(const ControlFSMData<float>& data) {
  const auto& estResult = data._stateEstimator->getResult();

  // 仅取 yaw 的近似，从I_body到I_world
  float yaw = estResult.rpy[2];
  float yc = std::cos(yaw), ys = std::sin(yaw);
  Eigen::Matrix<float,3,3> R_yaw;
  R_yaw << yc, -ys, 0,
           ys,  yc, 0,
            0,   0, 1;
  I_world = R_yaw * I_body * R_yaw.transpose();
  Eigen::Matrix<float,3,3> I_inv = I_world.inverse();

  const Vec3<float>& pCOM_world = estResult.position; // 世界系
  const Mat3<float>& R_wb = estResult.rBody; // world->body

  Vec3<float> tau_contact_total = Vec3<float>::Zero();

  for (int i = 0; i < 4; i++) {
    // 机体到世界：hip + foot pos（机体系）旋到世界，这里的hip位置就是相对于机身的
    Vec3<float> pFoot_world =  estResult.position + estResult.rBody.transpose() * (data._quadruped->getHipLocation(i) + 
                                data._legController->datas[i].p);
    pFoot[i] = pFoot_world - estResult.position;  // 计算相对于COM的位置（世界系）
    // 脚端力（机体系）→ 世界系，再取作用在机体上的反作用力（取负号）
    Vec3<float> F_body  = foot_force_tau_est[i];        // 脚受到的力（机体系），地面对脚
    Vec3<float> F_world = bodyToWorld(R_wb, F_body);    // 世界系
    Vec3<float> F_on_body_world = -F_world;             // 作用在机体上的力
    if(contact_state[i] > 0.f) {
      tau_contact_total += skew3(pFoot[i]) * F_on_body_world;
    }
  }

  // 负载重力矩（示例）
  float mp_real = 5.0f;
  Vec3<float> payload_position_base(-0.15f, 0.0f, 0.08f);
  Vec3<float> payload_position_world = R_wb.transpose() * payload_position_base;
  Vec3<float> gravity_world(0.0f, 0.0f, -9.81f);
  Vec3<float> payload_gravity_force = mp_real * gravity_world;
  Vec3<float> tau_gravity = skew3(payload_position_world) * payload_gravity_force;

  d_real = I_inv * tau_gravity; // 仅用于对比打印

  // 1) 期望姿态：这里只跟踪 yaw，roll/pitch 设为 0（可按需要替换）
  Eigen::Vector3f rpy_des;
  rpy_des << 0.f, 0.f, _yaw_des;

  //x_over = 当前欧拉角 - 期望欧拉角 = x1-x1_des;
  Eigen::Vector3f x_over;
  x_over << estResult.rpy[0] - 0.f,
            estResult.rpy[1] - 0.f,
            estResult.rpy[2] - _yaw_des;

  Eigen::Vector3f x2;
  x2 << estResult.omegaWorld[0],
        estResult.omegaWorld[1],
        estResult.omegaWorld[2];

  Eigen::Matrix3f Lambda = Eigen::Matrix3f::Identity() * 7.f;   // 原来 100 可先降一点更稳
  Eigen::Matrix3f C      = Eigen::Matrix3f::Identity() * 7.f;   // 漂移抑制

  // 5) 滑模面 s
  Eigen::Vector3f s = x2 + Lambda * x_over; // 6) 滑模面 s 的导数

  Vec3<float> d_hat_dot = K * (C *s + Lambda * x_2  - K *x_2);

  const float d_dot_limit = 20.f; // rad/s^2/s，按需要调整

  for(int i=0;i<3;i++){
    d_hat_dot[i] = std::max(std::min(d_hat_dot[i], d_dot_limit), -d_dot_limit);
  }
  // Vec3<float> d_hat_dot = -K * ( I_inv * tau_contact_total + d_est + beta );
  d_est += dt * d_hat_dot;

  // 添加调试输出
  if (enable_pid_debug){
    static int counter = 0;
    if (counter++ % debug_print_frequency == 0) {
        std::cout << "=== Disturbance Estimator Debug ===\n";
        std::cout << "tau_contact_total: " << tau_contact_total.transpose() << "\n";
        std::cout << "tau_gravity: " << tau_gravity.transpose() << "\n";
        std::cout << "d_est : " << d_est.transpose()  << "\n";
        std::cout << "d_real: " << d_real.transpose() << "\n";
        // 打印脚部位置
        for(int i = 0; i < 4; i++) {
        std::cout << "pFoot[" << i << "]: " << pFoot[i].transpose() << "\n";
        std::cout << "contact_state[" << "]" <<  contact_state[i] << " "; // 打印接触状态
        }
        std::cout << std::endl;
    }
  }
}

/* ============================================================= */
/*                            QP 力分配                           */
/* ============================================================= */

void DisturbanceEstimator::solveQP() {
  // 统计接触腿数量（仅用于兜底与日志；固定规模与此无关）
  int num_contact_legs = 0;
  for (int i = 0; i < 4; ++i) if (contact_state[i] > 0.f) num_contact_legs++;
  if (num_contact_legs == 0) {
    for (int i = 0; i < 4; ++i) foot_force_des[i].setZero();
    return;
  }

  // A_eq(6x12)：上3行力平衡；下3行力矩平衡（使用 r_world = pFoot[i]）
  Eigen::Matrix<double, 6, kNV> A_eq;
  A_eq.setZero();
  for (int leg = 0; leg < kLegs; ++leg) {
    A_eq.block<3,3>(0, 3*leg) = Eigen::Matrix3d::Identity();
  }
  for (int leg = 0; leg < kLegs; ++leg) {
    Eigen::Vector3d r = pFoot[leg].cast<double>(); // 相对COM的世界系位置（已在updateTauEst保存）
    A_eq.block<3,3>(3, 3*leg) = skew3(r);
  }

  Eigen::Matrix<double,6,1> b_eq;
  b_eq.segment<3>(0) = position_control_force.cast<double>();   // 世界系合力
  b_eq.segment<3>(3) = attitude_control_torque.cast<double>();  // 世界系力矩

  // A_ineq(20x12)：线性摩擦锥 |Fx|<=mu Fz, |Fy|<=mu Fz, Fz>=0
  Eigen::Matrix<double, kNC, kNV> A_ineq;
  A_ineq.setZero();
  Eigen::Matrix<double, kIneqPerLeg, 3> Fblock;
  Fblock <<
      1.0,  0.0, -mu,
     -1.0,  0.0, -mu,
      0.0,  1.0, -mu,
      0.0, -1.0, -mu,
      0.0,  0.0, -1.0;
  for (int leg = 0; leg < kLegs; ++leg) {
    A_ineq.block<kIneqPerLeg,3>(kIneqPerLeg*leg, 3*leg) = Fblock;
  }

  // 变量边界：摆动腿 lb=ub=0；站立腿 Fx,Fy∈[-f_max,f_max], Fz∈[kFzMin,f_max]
  Eigen::Matrix<double, kNV, 1> lb, ub;
  for (int leg = 0; leg < kLegs; ++leg) {
    bool in_contact = (contact_state[leg] > 0.f);
    if (in_contact) {
      lb(3*leg+0) = -f_max;  ub(3*leg+0) =  f_max;
      lb(3*leg+1) = -f_max;  ub(3*leg+1) =  f_max;
      lb(3*leg+2) =  kFzMinBound; ub(3*leg+2) =  f_max;
    } else {
      lb.segment<3>(3*leg).setZero();
      ub.segment<3>(3*leg).setZero();
    }
  }

  // 不等式边界：A*x <= 0；摆动腿的 5 行放宽为 [-Huge, +Huge]
  Eigen::Matrix<double, kNC, 1> lbA, ubA;
  lbA.setConstant(-kHugeBound);
  ubA.setZero();
  for (int leg = 0; leg < kLegs; ++leg) {
    if (contact_state[leg] <= 0.f) {
      lbA.segment<kIneqPerLeg>(kIneqPerLeg*leg).setConstant(-kHugeBound);
      ubA.segment<kIneqPerLeg>(kIneqPerLeg*leg).setConstant( kHugeBound);
    }
  }

  // 目标 H, g
  Eigen::Matrix<double, kNV, kNV> H;
  Eigen::Matrix<double, kNV, 1> g;
  Eigen::Matrix<double, 6, 6>  Q = kQEqWeight * Eigen::Matrix<double,6,6>::Identity();
  Eigen::Matrix<double, kNV, kNV> R = kRDiag     * Eigen::Matrix<double, kNV, kNV>::Identity();
  H = A_eq.transpose() * Q * A_eq + R;
  g = -A_eq.transpose() * Q * b_eq;

  // 打包到原生数组
  qpOASES::real_t H_qp[kNV*kNV], g_qp[kNV], A_qp[kNC*kNV];
  qpOASES::real_t lb_qp[kNV], ub_qp[kNV], lbA_qp[kNC], ubA_qp[kNC];
  for (int r = 0; r < kNV; ++r) {
    g_qp[r]  = g(r);
    lb_qp[r] = lb(r);
    ub_qp[r] = ub(r);
    for (int c = 0; c < kNV; ++c) H_qp[r*kNV + c] = H(r,c);
  }
  for (int r = 0; r < kNC; ++r) {
    lbA_qp[r] = lbA(r);
    ubA_qp[r] = ubA(r);
    for (int c = 0; c < kNV; ++c) A_qp[r*kNV + c] = A_ineq(r,c);
  }

  qpOASES::int_t  nWSR = kWSRMax;
  qpOASES::real_t cput = kCpuTimeMax;

  qpOASES::returnValue ret;
  if (!qp_initialized_) {
    ret = qp_->init(H_qp, g_qp, A_qp, lb_qp, ub_qp, lbA_qp, ubA_qp, nWSR, &cput);
    qp_initialized_ = (ret == qpOASES::SUCCESSFUL_RETURN);
  } else {
    ret = qp_->hotstart(H_qp, g_qp, A_qp, lb_qp, ub_qp, lbA_qp, ubA_qp, nWSR, &cput);
    if (ret != qpOASES::SUCCESSFUL_RETURN) {
      // 退回 init（极少数情况下）
      nWSR = kWSRMax;
      cput = kCpuTimeMax;
      ret = qp_->init(H_qp, g_qp, A_qp, lb_qp, ub_qp, lbA_qp, ubA_qp, nWSR, &cput);
      qp_initialized_ = (ret == qpOASES::SUCCESSFUL_RETURN);
    }
  }

  // 输出与兜底
  for (int i = 0; i < 4; ++i) foot_force_des[i].setZero();
  if (ret == qpOASES::SUCCESSFUL_RETURN) {
    qpOASES::real_t sol[kNV];
    qp_->getPrimalSolution(sol);
    for (int leg = 0; leg < kLegs; ++leg) {
      foot_force_des[leg][0] = (float)sol[3*leg + 0];
      foot_force_des[leg][1] = (float)sol[3*leg + 1];
      foot_force_des[leg][2] = (float)sol[3*leg + 2];
    }
  } else {
    std::cout << "[QP] failed ret=" << (int)ret
              << " nWSR=" << nWSR
              << " cpu="  << (double)cput*1000.0 << " ms\n";
    int cnum = 0; for (int i = 0; i < 4; ++i) if (contact_state[i] > 0.f) cnum++;
    if (cnum > 0) {
      float Fz_each = 140.f / cnum; // 兜底：总重≈140N
      for (int i = 0; i < 4; ++i) if (contact_state[i] > 0.f) foot_force_des[i][2] = Fz_each;
    }
  }

  // 统计（毫秒单位）
  static double total_time_s = 0.0;
  static int    counter = 0;
  total_time_s += (double)cput;
  if (enable_pid_debug && (++counter % debug_print_frequency == 0)) {
    double avg_ms = (total_time_s / counter) * 1000.0;
    std::cout << "=== QP Solver Debug ===\n";
    std::cout << "Average QP time: " << avg_ms << " ms\n";
    for (int leg = 0; leg < kLegs; ++leg) {
      std::cout << "Foot force des [leg " << leg << "]: "
                << foot_force_des[leg].transpose() << std::endl;
    }
    std::cout << "F_b = " << position_control_force.transpose()
          << ", T_b = " << attitude_control_torque.transpose() << "\n";
    for(int i=0;i<4;i++)
        if(contact_state[i]>0.f)
            std::cout << "r["<<i<<"]="<< pFoot[i].transpose() << "\n"; // r 为世界系相对COM

    for(int i=0;i<4;i++)
        std::cout << "F_est["<<i<<"]="<< F_world_tau_est[i] << "\n";
    total_time_s = 0.0;
    counter = 0;
  }
}

/* -------------------- 数据记录功能实现 -------------------- */

void DisturbanceEstimator::enableDataLogging(bool enable, const std::string& filename) {
    enable_data_logging = enable;
    // 定义目标目录
    const std::string log_dir = "/home/<USER>/Desktop/03_mitcontrol_latest_real/logger/";
    if (enable) {
        // 生成文件名
        if (filename.empty()) {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::stringstream ss;
            ss << "disturbance_estimator_data_"
               << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
               << ".csv";
            log_filename = log_dir + ss.str();
        } else {
            log_filename = filename;
        }

        // 确保目录存在
        std::filesystem::create_directories(log_dir);

        // 打开文件并写入头部
        log_file.open(log_filename);
        if (log_file.is_open()) {
            log_file << "timestamp,mp_fused,mp_torque_based,mp,torque_confidence,total_contact_force,"
                     << "acc_world_x,acc_world_y,acc_world_z,"
                     << "disturbance_x,disturbance_y,disturbance_z,"
                     << "d_est_x,d_est_y,d_est_z\n";
            log_file.flush();

            // 记录开始时间
            logging_start_time = std::chrono::duration<float>(
                std::chrono::steady_clock::now().time_since_epoch()).count();
            logging_counter = 0;

            // 清空向量数据
            logged_timestamps.clear();
            logged_mp_fused.clear();
            logged_mp_torque_based.clear();
            logged_mp.clear();
            logged_torque_confidence.clear();
            logged_total_contact_force.clear();
            logged_acc_world_3d.clear();
            logged_disturbance.clear();
            logged_d_est.clear();

            std::cout << "[DisturbanceEstimator] 数据记录已启用，文件: " << log_filename << std::endl;
        } else {
            std::cerr << "[DisturbanceEstimator] 错误：无法打开文件 " << log_filename << std::endl;
            enable_data_logging = false;
        }
    } else {
        // 关闭记录
        if (log_file.is_open()) {
            log_file.close();
            std::cout << "[DisturbanceEstimator] 数据记录已停止，共记录 "
                      << logging_counter << " 个数据点" << std::endl;
        }
    }
}

void DisturbanceEstimator::logData() {
    if (!enable_data_logging || !log_file.is_open()) {
        return;
    }

    // 计算时间戳
    float current_time = std::chrono::duration<float>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    float timestamp = current_time - logging_start_time;

    // 记录到向量（用于后续分析）
    logged_timestamps.push_back(timestamp);
    logged_mp_fused.push_back(mp_fused);
    logged_mp_torque_based.push_back(mp_torque_based);
    logged_mp.push_back(mp);
    logged_torque_confidence.push_back(torque_confidence);
    logged_total_contact_force.push_back(total_contact_force);
    logged_acc_world_3d.push_back(acc_world_3d);
    logged_disturbance.push_back(disturbance);
    logged_d_est.push_back(d_est);

    // 写入文件
    log_file << std::fixed << std::setprecision(6)
             << timestamp << ","
             << mp_fused << ","
             << mp_torque_based << ","
             << mp << ","
             << torque_confidence << ","
             << total_contact_force << ","
             << acc_world_3d[0] << "," << acc_world_3d[1] << "," << acc_world_3d[2] << ","
             << disturbance[0] << "," << disturbance[1] << "," << disturbance[2] << ","
             << d_est[0] << "," << d_est[1] << "," << d_est[2] << "\n";

    logging_counter++;

    // 定期刷新文件
    if (logging_counter % 100 == 0) {
        log_file.flush();
    }
}

void DisturbanceEstimator::saveDataToCSV(const std::string& filename) {
    std::string output_filename = filename;
    if (output_filename.empty()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << "disturbance_estimator_export_"
           << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
           << ".csv";
        output_filename = ss.str();
    }

    std::ofstream file(output_filename);
    if (!file.is_open()) {
        std::cerr << "[DisturbanceEstimator] 错误：无法创建文件 " << output_filename << std::endl;
        return;
    }

    // 写入头部
    file << "timestamp,mp_fused,mp_torque_based,mp,torque_confidence,total_contact_force,"
         << "acc_world_x,acc_world_y,acc_world_z,"
         << "disturbance_x,disturbance_y,disturbance_z,"
         << "d_est_x,d_est_y,d_est_z\n";

    // 写入数据
    size_t data_size = std::min({logged_timestamps.size(), logged_mp_fused.size(),
                                logged_mp_torque_based.size(), logged_mp.size(),
                                logged_torque_confidence.size(), logged_total_contact_force.size(),
                                logged_acc_world_3d.size(), logged_disturbance.size(),
                                logged_d_est.size()});

    for (size_t i = 0; i < data_size; ++i) {
        file << std::fixed << std::setprecision(6)
             << logged_timestamps[i] << ","
             << logged_mp_fused[i] << ","
             << logged_mp_torque_based[i] << ","
             << logged_mp[i] << ","
             << logged_torque_confidence[i] << ","
             << logged_total_contact_force[i] << ","
             << logged_acc_world_3d[i][0] << "," << logged_acc_world_3d[i][1] << "," << logged_acc_world_3d[i][2] << ","
             << logged_disturbance[i][0] << "," << logged_disturbance[i][1] << "," << logged_disturbance[i][2] << ","
             << logged_d_est[i][0] << "," << logged_d_est[i][1] << "," << logged_d_est[i][2] << "\n";
    }

    file.close();
    std::cout << "[DisturbanceEstimator] 数据已导出到: " << output_filename
              << " (共 " << data_size << " 个数据点)" << std::endl;
}