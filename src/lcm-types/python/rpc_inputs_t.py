"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class rpc_inputs_t(object):
    __slots__ = ["current_state", "p_foot0", "r_hip", "x_desired", "gait_enabled", "phase_variable", "period_time", "time_swing", "mass", "inertia", "gravity", "mu", "z_g", "Q", "R", "K_HX"]

    __typenames__ = ["double", "double", "double", "double", "double", "double", "double", "double", "double", "double", "double", "double", "double", "double", "double", "double"]

    __dimensions__ = [[12], [12], [12], [72], [4], [4], [4], [4], None, [3], [3], [4], [4], [12], [24], [30]]

    def __init__(self):
        self.current_state = [ 0.0 for dim0 in range(12) ]
        self.p_foot0 = [ 0.0 for dim0 in range(12) ]
        self.r_hip = [ 0.0 for dim0 in range(12) ]
        self.x_desired = [ 0.0 for dim0 in range(72) ]
        self.gait_enabled = [ 0.0 for dim0 in range(4) ]
        self.phase_variable = [ 0.0 for dim0 in range(4) ]
        self.period_time = [ 0.0 for dim0 in range(4) ]
        self.time_swing = [ 0.0 for dim0 in range(4) ]
        self.mass = 0.0
        self.inertia = [ 0.0 for dim0 in range(3) ]
        self.gravity = [ 0.0 for dim0 in range(3) ]
        self.mu = [ 0.0 for dim0 in range(4) ]
        self.z_g = [ 0.0 for dim0 in range(4) ]
        self.Q = [ 0.0 for dim0 in range(12) ]
        self.R = [ 0.0 for dim0 in range(24) ]
        self.K_HX = [ 0.0 for dim0 in range(30) ]

    def encode(self):
        buf = BytesIO()
        buf.write(rpc_inputs_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>12d', *self.current_state[:12]))
        buf.write(struct.pack('>12d', *self.p_foot0[:12]))
        buf.write(struct.pack('>12d', *self.r_hip[:12]))
        buf.write(struct.pack('>72d', *self.x_desired[:72]))
        buf.write(struct.pack('>4d', *self.gait_enabled[:4]))
        buf.write(struct.pack('>4d', *self.phase_variable[:4]))
        buf.write(struct.pack('>4d', *self.period_time[:4]))
        buf.write(struct.pack('>4d', *self.time_swing[:4]))
        buf.write(struct.pack(">d", self.mass))
        buf.write(struct.pack('>3d', *self.inertia[:3]))
        buf.write(struct.pack('>3d', *self.gravity[:3]))
        buf.write(struct.pack('>4d', *self.mu[:4]))
        buf.write(struct.pack('>4d', *self.z_g[:4]))
        buf.write(struct.pack('>12d', *self.Q[:12]))
        buf.write(struct.pack('>24d', *self.R[:24]))
        buf.write(struct.pack('>30d', *self.K_HX[:30]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != rpc_inputs_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return rpc_inputs_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = rpc_inputs_t()
        self.current_state = struct.unpack('>12d', buf.read(96))
        self.p_foot0 = struct.unpack('>12d', buf.read(96))
        self.r_hip = struct.unpack('>12d', buf.read(96))
        self.x_desired = struct.unpack('>72d', buf.read(576))
        self.gait_enabled = struct.unpack('>4d', buf.read(32))
        self.phase_variable = struct.unpack('>4d', buf.read(32))
        self.period_time = struct.unpack('>4d', buf.read(32))
        self.time_swing = struct.unpack('>4d', buf.read(32))
        self.mass = struct.unpack(">d", buf.read(8))[0]
        self.inertia = struct.unpack('>3d', buf.read(24))
        self.gravity = struct.unpack('>3d', buf.read(24))
        self.mu = struct.unpack('>4d', buf.read(32))
        self.z_g = struct.unpack('>4d', buf.read(32))
        self.Q = struct.unpack('>12d', buf.read(96))
        self.R = struct.unpack('>24d', buf.read(192))
        self.K_HX = struct.unpack('>30d', buf.read(240))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if rpc_inputs_t in parents: return 0
        tmphash = (0x139d9ad50b349131) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if rpc_inputs_t._packed_fingerprint is None:
            rpc_inputs_t._packed_fingerprint = struct.pack(">Q", rpc_inputs_t._get_hash_recursive([]))
        return rpc_inputs_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

