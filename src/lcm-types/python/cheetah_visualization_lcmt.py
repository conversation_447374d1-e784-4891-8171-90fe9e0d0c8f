"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class cheetah_visualization_lcmt(object):
    __slots__ = ["q", "x", "quat", "rgba"]

    __typenames__ = ["float", "float", "float", "float"]

    __dimensions__ = [[12], [3], [4], [4]]

    def __init__(self):
        self.q = [ 0.0 for dim0 in range(12) ]
        self.x = [ 0.0 for dim0 in range(3) ]
        self.quat = [ 0.0 for dim0 in range(4) ]
        self.rgba = [ 0.0 for dim0 in range(4) ]

    def encode(self):
        buf = BytesIO()
        buf.write(cheetah_visualization_lcmt._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>12f', *self.q[:12]))
        buf.write(struct.pack('>3f', *self.x[:3]))
        buf.write(struct.pack('>4f', *self.quat[:4]))
        buf.write(struct.pack('>4f', *self.rgba[:4]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != cheetah_visualization_lcmt._get_packed_fingerprint():
            raise ValueError("Decode error")
        return cheetah_visualization_lcmt._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = cheetah_visualization_lcmt()
        self.q = struct.unpack('>12f', buf.read(48))
        self.x = struct.unpack('>3f', buf.read(12))
        self.quat = struct.unpack('>4f', buf.read(16))
        self.rgba = struct.unpack('>4f', buf.read(16))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if cheetah_visualization_lcmt in parents: return 0
        tmphash = (0xe9e9209bb36f494f) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if cheetah_visualization_lcmt._packed_fingerprint is None:
            cheetah_visualization_lcmt._packed_fingerprint = struct.pack(">Q", cheetah_visualization_lcmt._get_hash_recursive([]))
        return cheetah_visualization_lcmt._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

