"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class rpc_data_clean_t(object):
    __slots__ = ["value"]

    __typenames__ = ["double"]

    __dimensions__ = [[186]]

    def __init__(self):
        self.value = [ 0.0 for dim0 in range(186) ]

    def encode(self):
        buf = BytesIO()
        buf.write(rpc_data_clean_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>186d', *self.value[:186]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != rpc_data_clean_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return rpc_data_clean_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = rpc_data_clean_t()
        self.value = struct.unpack('>186d', buf.read(1488))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if rpc_data_clean_t in parents: return 0
        tmphash = (0x45679a665b25fc80) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if rpc_data_clean_t._packed_fingerprint is None:
            rpc_data_clean_t._packed_fingerprint = struct.pack(">Q", rpc_data_clean_t._get_hash_recursive([]))
        return rpc_data_clean_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

