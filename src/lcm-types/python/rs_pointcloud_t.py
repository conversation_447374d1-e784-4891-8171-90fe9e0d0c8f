"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class rs_pointcloud_t(object):
    __slots__ = ["pointlist"]

    __typenames__ = ["double"]

    __dimensions__ = [[5001, 3]]

    def __init__(self):
        self.pointlist = [ [ 0.0 for dim1 in range(3) ] for dim0 in range(5001) ]

    def encode(self):
        buf = BytesIO()
        buf.write(rs_pointcloud_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        for i0 in range(5001):
            buf.write(struct.pack('>3d', *self.pointlist[i0][:3]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != rs_pointcloud_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return rs_pointcloud_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = rs_pointcloud_t()
        self.pointlist = []
        for i0 in range(5001):
            self.pointlist.append(struct.unpack('>3d', buf.read(24)))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if rs_pointcloud_t in parents: return 0
        tmphash = (0x960bbc9070e3509e) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if rs_pointcloud_t._packed_fingerprint is None:
            rs_pointcloud_t._packed_fingerprint = struct.pack(">Q", rs_pointcloud_t._get_hash_recursive([]))
        return rs_pointcloud_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

