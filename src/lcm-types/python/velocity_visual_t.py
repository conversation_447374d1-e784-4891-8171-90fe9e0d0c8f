"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class velocity_visual_t(object):
    __slots__ = ["vel_cmd", "base_position"]

    __typenames__ = ["double", "double"]

    __dimensions__ = [[3], [3]]

    def __init__(self):
        self.vel_cmd = [ 0.0 for dim0 in range(3) ]
        self.base_position = [ 0.0 for dim0 in range(3) ]

    def encode(self):
        buf = BytesIO()
        buf.write(velocity_visual_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>3d', *self.vel_cmd[:3]))
        buf.write(struct.pack('>3d', *self.base_position[:3]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != velocity_visual_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return velocity_visual_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = velocity_visual_t()
        self.vel_cmd = struct.unpack('>3d', buf.read(24))
        self.base_position = struct.unpack('>3d', buf.read(24))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if velocity_visual_t in parents: return 0
        tmphash = (0x939f71540029b78b) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if velocity_visual_t._packed_fingerprint is None:
            velocity_visual_t._packed_fingerprint = struct.pack(">Q", velocity_visual_t._get_hash_recursive([]))
        return velocity_visual_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

