"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class T265position_t(object):
    __slots__ = ["posBody", "velBody", "rpyBOdy", "omegaBody", "accBody"]

    __typenames__ = ["float", "float", "float", "float", "float"]

    __dimensions__ = [[3], [3], [3], [3], [3]]

    def __init__(self):
        self.posBody = [ 0.0 for dim0 in range(3) ]
        self.velBody = [ 0.0 for dim0 in range(3) ]
        self.rpyBOdy = [ 0.0 for dim0 in range(3) ]
        self.omegaBody = [ 0.0 for dim0 in range(3) ]
        self.accBody = [ 0.0 for dim0 in range(3) ]

    def encode(self):
        buf = BytesIO()
        buf.write(T265position_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>3f', *self.posBody[:3]))
        buf.write(struct.pack('>3f', *self.velBody[:3]))
        buf.write(struct.pack('>3f', *self.rpyBOdy[:3]))
        buf.write(struct.pack('>3f', *self.omegaBody[:3]))
        buf.write(struct.pack('>3f', *self.accBody[:3]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != T265position_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return T265position_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = T265position_t()
        self.posBody = struct.unpack('>3f', buf.read(12))
        self.velBody = struct.unpack('>3f', buf.read(12))
        self.rpyBOdy = struct.unpack('>3f', buf.read(12))
        self.omegaBody = struct.unpack('>3f', buf.read(12))
        self.accBody = struct.unpack('>3f', buf.read(12))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if T265position_t in parents: return 0
        tmphash = (0x9aa722eb9173f1a4) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if T265position_t._packed_fingerprint is None:
            T265position_t._packed_fingerprint = struct.pack(">Q", T265position_t._get_hash_recursive([]))
        return T265position_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

