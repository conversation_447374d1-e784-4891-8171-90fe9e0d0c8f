"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class control_parameter_request_lcmt(object):
    __slots__ = ["name", "requestNumber", "value", "parameterKind", "requestKind"]

    __typenames__ = ["int8_t", "int64_t", "int8_t", "int8_t", "int8_t"]

    __dimensions__ = [[64], None, [64], None, None]

    def __init__(self):
        self.name = [ 0 for dim0 in range(64) ]
        self.requestNumber = 0
        self.value = [ 0 for dim0 in range(64) ]
        self.parameterKind = 0
        self.requestKind = 0

    def encode(self):
        buf = BytesIO()
        buf.write(control_parameter_request_lcmt._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>64b', *self.name[:64]))
        buf.write(struct.pack(">q", self.requestNumber))
        buf.write(struct.pack('>64b', *self.value[:64]))
        buf.write(struct.pack(">bb", self.parameterKind, self.requestKind))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != control_parameter_request_lcmt._get_packed_fingerprint():
            raise ValueError("Decode error")
        return control_parameter_request_lcmt._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = control_parameter_request_lcmt()
        self.name = struct.unpack('>64b', buf.read(64))
        self.requestNumber = struct.unpack(">q", buf.read(8))[0]
        self.value = struct.unpack('>64b', buf.read(64))
        self.parameterKind, self.requestKind = struct.unpack(">bb", buf.read(2))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if control_parameter_request_lcmt in parents: return 0
        tmphash = (0xe827e9f6525296b9) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if control_parameter_request_lcmt._packed_fingerprint is None:
            control_parameter_request_lcmt._packed_fingerprint = struct.pack(">Q", control_parameter_request_lcmt._get_hash_recursive([]))
        return control_parameter_request_lcmt._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

