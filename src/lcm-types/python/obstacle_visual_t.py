"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class obstacle_visual_t(object):
    __slots__ = ["num_obs", "location", "sigma", "height", "mesh_center_pos"]

    __typenames__ = ["int32_t", "double", "double", "double", "double"]

    __dimensions__ = [None, [100, 3], None, None, [3]]

    def __init__(self):
        self.num_obs = 0
        self.location = [ [ 0.0 for dim1 in range(3) ] for dim0 in range(100) ]
        self.sigma = 0.0
        self.height = 0.0
        self.mesh_center_pos = [ 0.0 for dim0 in range(3) ]

    def encode(self):
        buf = BytesIO()
        buf.write(obstacle_visual_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack(">i", self.num_obs))
        for i0 in range(100):
            buf.write(struct.pack('>3d', *self.location[i0][:3]))
        buf.write(struct.pack(">dd", self.sigma, self.height))
        buf.write(struct.pack('>3d', *self.mesh_center_pos[:3]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != obstacle_visual_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return obstacle_visual_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = obstacle_visual_t()
        self.num_obs = struct.unpack(">i", buf.read(4))[0]
        self.location = []
        for i0 in range(100):
            self.location.append(struct.unpack('>3d', buf.read(24)))
        self.sigma, self.height = struct.unpack(">dd", buf.read(16))
        self.mesh_center_pos = struct.unpack('>3d', buf.read(24))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if obstacle_visual_t in parents: return 0
        tmphash = (0xf74f22add035377d) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if obstacle_visual_t._packed_fingerprint is None:
            obstacle_visual_t._packed_fingerprint = struct.pack(">Q", obstacle_visual_t._get_hash_recursive([]))
        return obstacle_visual_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

