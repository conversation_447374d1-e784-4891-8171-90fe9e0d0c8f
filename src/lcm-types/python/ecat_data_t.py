"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class ecat_data_t(object):
    __slots__ = ["x", "y", "z", "dx", "dy", "dz", "fx", "fy", "fz", "q_abad", "q_hip", "q_knee", "dq_abad", "dq_hip", "dq_knee", "tau_abad", "tau_hip", "tau_knee", "tau_des_abad", "tau_des_hip", "tau_des_knee", "loop_count_ti", "ethercat_count_ti", "microtime_ti"]

    __typenames__ = ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "int16_t", "int16_t", "int16_t"]

    __dimensions__ = [[4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4]]

    def __init__(self):
        self.x = [ 0.0 for dim0 in range(4) ]
        self.y = [ 0.0 for dim0 in range(4) ]
        self.z = [ 0.0 for dim0 in range(4) ]
        self.dx = [ 0.0 for dim0 in range(4) ]
        self.dy = [ 0.0 for dim0 in range(4) ]
        self.dz = [ 0.0 for dim0 in range(4) ]
        self.fx = [ 0.0 for dim0 in range(4) ]
        self.fy = [ 0.0 for dim0 in range(4) ]
        self.fz = [ 0.0 for dim0 in range(4) ]
        self.q_abad = [ 0.0 for dim0 in range(4) ]
        self.q_hip = [ 0.0 for dim0 in range(4) ]
        self.q_knee = [ 0.0 for dim0 in range(4) ]
        self.dq_abad = [ 0.0 for dim0 in range(4) ]
        self.dq_hip = [ 0.0 for dim0 in range(4) ]
        self.dq_knee = [ 0.0 for dim0 in range(4) ]
        self.tau_abad = [ 0.0 for dim0 in range(4) ]
        self.tau_hip = [ 0.0 for dim0 in range(4) ]
        self.tau_knee = [ 0.0 for dim0 in range(4) ]
        self.tau_des_abad = [ 0.0 for dim0 in range(4) ]
        self.tau_des_hip = [ 0.0 for dim0 in range(4) ]
        self.tau_des_knee = [ 0.0 for dim0 in range(4) ]
        self.loop_count_ti = [ 0 for dim0 in range(4) ]
        self.ethercat_count_ti = [ 0 for dim0 in range(4) ]
        self.microtime_ti = [ 0 for dim0 in range(4) ]

    def encode(self):
        buf = BytesIO()
        buf.write(ecat_data_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>4f', *self.x[:4]))
        buf.write(struct.pack('>4f', *self.y[:4]))
        buf.write(struct.pack('>4f', *self.z[:4]))
        buf.write(struct.pack('>4f', *self.dx[:4]))
        buf.write(struct.pack('>4f', *self.dy[:4]))
        buf.write(struct.pack('>4f', *self.dz[:4]))
        buf.write(struct.pack('>4f', *self.fx[:4]))
        buf.write(struct.pack('>4f', *self.fy[:4]))
        buf.write(struct.pack('>4f', *self.fz[:4]))
        buf.write(struct.pack('>4f', *self.q_abad[:4]))
        buf.write(struct.pack('>4f', *self.q_hip[:4]))
        buf.write(struct.pack('>4f', *self.q_knee[:4]))
        buf.write(struct.pack('>4f', *self.dq_abad[:4]))
        buf.write(struct.pack('>4f', *self.dq_hip[:4]))
        buf.write(struct.pack('>4f', *self.dq_knee[:4]))
        buf.write(struct.pack('>4f', *self.tau_abad[:4]))
        buf.write(struct.pack('>4f', *self.tau_hip[:4]))
        buf.write(struct.pack('>4f', *self.tau_knee[:4]))
        buf.write(struct.pack('>4f', *self.tau_des_abad[:4]))
        buf.write(struct.pack('>4f', *self.tau_des_hip[:4]))
        buf.write(struct.pack('>4f', *self.tau_des_knee[:4]))
        buf.write(struct.pack('>4h', *self.loop_count_ti[:4]))
        buf.write(struct.pack('>4h', *self.ethercat_count_ti[:4]))
        buf.write(struct.pack('>4h', *self.microtime_ti[:4]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != ecat_data_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return ecat_data_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = ecat_data_t()
        self.x = struct.unpack('>4f', buf.read(16))
        self.y = struct.unpack('>4f', buf.read(16))
        self.z = struct.unpack('>4f', buf.read(16))
        self.dx = struct.unpack('>4f', buf.read(16))
        self.dy = struct.unpack('>4f', buf.read(16))
        self.dz = struct.unpack('>4f', buf.read(16))
        self.fx = struct.unpack('>4f', buf.read(16))
        self.fy = struct.unpack('>4f', buf.read(16))
        self.fz = struct.unpack('>4f', buf.read(16))
        self.q_abad = struct.unpack('>4f', buf.read(16))
        self.q_hip = struct.unpack('>4f', buf.read(16))
        self.q_knee = struct.unpack('>4f', buf.read(16))
        self.dq_abad = struct.unpack('>4f', buf.read(16))
        self.dq_hip = struct.unpack('>4f', buf.read(16))
        self.dq_knee = struct.unpack('>4f', buf.read(16))
        self.tau_abad = struct.unpack('>4f', buf.read(16))
        self.tau_hip = struct.unpack('>4f', buf.read(16))
        self.tau_knee = struct.unpack('>4f', buf.read(16))
        self.tau_des_abad = struct.unpack('>4f', buf.read(16))
        self.tau_des_hip = struct.unpack('>4f', buf.read(16))
        self.tau_des_knee = struct.unpack('>4f', buf.read(16))
        self.loop_count_ti = struct.unpack('>4h', buf.read(8))
        self.ethercat_count_ti = struct.unpack('>4h', buf.read(8))
        self.microtime_ti = struct.unpack('>4h', buf.read(8))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if ecat_data_t in parents: return 0
        tmphash = (0x2dd37f039d5cbafc) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if ecat_data_t._packed_fingerprint is None:
            ecat_data_t._packed_fingerprint = struct.pack(">Q", ecat_data_t._get_hash_recursive([]))
        return ecat_data_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

