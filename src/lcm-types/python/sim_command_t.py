"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class sim_command_t(object):
    __slots__ = ["command_number", "data_size", "data"]

    __typenames__ = ["int32_t", "int32_t", "double"]

    __dimensions__ = [None, None, ["data_size"]]

    def __init__(self):
        self.command_number = 0
        self.data_size = 0
        self.data = []

    def encode(self):
        buf = BytesIO()
        buf.write(sim_command_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack(">ii", self.command_number, self.data_size))
        buf.write(struct.pack('>%dd' % self.data_size, *self.data[:self.data_size]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != sim_command_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return sim_command_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = sim_command_t()
        self.command_number, self.data_size = struct.unpack(">ii", buf.read(8))
        self.data = struct.unpack('>%dd' % self.data_size, buf.read(self.data_size * 8))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if sim_command_t in parents: return 0
        tmphash = (0x32e692d220533c5e) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if sim_command_t._packed_fingerprint is None:
            sim_command_t._packed_fingerprint = struct.pack(">Q", sim_command_t._get_hash_recursive([]))
        return sim_command_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

