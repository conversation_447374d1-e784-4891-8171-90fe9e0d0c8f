"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class microstrain_lcmt(object):
    __slots__ = ["quat", "rpy", "omega", "acc", "good_packets", "bad_packets", "foot_force_est"]

    __typenames__ = ["float", "float", "float", "float", "int64_t", "int64_t", "float"]

    __dimensions__ = [[4], [3], [3], [3], None, None, [4]]

    def __init__(self):
        self.quat = [ 0.0 for dim0 in range(4) ]
        self.rpy = [ 0.0 for dim0 in range(3) ]
        self.omega = [ 0.0 for dim0 in range(3) ]
        self.acc = [ 0.0 for dim0 in range(3) ]
        self.good_packets = 0
        self.bad_packets = 0
        self.foot_force_est = [ 0.0 for dim0 in range(4) ]

    def encode(self):
        buf = BytesIO()
        buf.write(microstrain_lcmt._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>4f', *self.quat[:4]))
        buf.write(struct.pack('>3f', *self.rpy[:3]))
        buf.write(struct.pack('>3f', *self.omega[:3]))
        buf.write(struct.pack('>3f', *self.acc[:3]))
        buf.write(struct.pack(">qq", self.good_packets, self.bad_packets))
        buf.write(struct.pack('>4f', *self.foot_force_est[:4]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != microstrain_lcmt._get_packed_fingerprint():
            raise ValueError("Decode error")
        return microstrain_lcmt._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = microstrain_lcmt()
        self.quat = struct.unpack('>4f', buf.read(16))
        self.rpy = struct.unpack('>3f', buf.read(12))
        self.omega = struct.unpack('>3f', buf.read(12))
        self.acc = struct.unpack('>3f', buf.read(12))
        self.good_packets, self.bad_packets = struct.unpack(">qq", buf.read(16))
        self.foot_force_est = struct.unpack('>4f', buf.read(16))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if microstrain_lcmt in parents: return 0
        tmphash = (0x91b811bfde6771b3) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if microstrain_lcmt._packed_fingerprint is None:
            microstrain_lcmt._packed_fingerprint = struct.pack(">Q", microstrain_lcmt._get_hash_recursive([]))
        return microstrain_lcmt._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

