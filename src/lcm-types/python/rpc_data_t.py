"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class rpc_data_t(object):
    __slots__ = ["cpu_opt_time_microseconds", "t_sent", "time_start", "dt_pred", "x_opt", "u_opt", "p_opt"]

    __typenames__ = ["double", "double", "double", "double", "double", "double", "double"]

    __dimensions__ = [None, None, None, [8], [8, 12], [8, 24], [3, 4]]

    def __init__(self):
        self.cpu_opt_time_microseconds = 0.0
        self.t_sent = 0.0
        self.time_start = 0.0
        self.dt_pred = [ 0.0 for dim0 in range(8) ]
        self.x_opt = [ [ 0.0 for dim1 in range(12) ] for dim0 in range(8) ]
        self.u_opt = [ [ 0.0 for dim1 in range(24) ] for dim0 in range(8) ]
        self.p_opt = [ [ 0.0 for dim1 in range(4) ] for dim0 in range(3) ]

    def encode(self):
        buf = BytesIO()
        buf.write(rpc_data_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack(">ddd", self.cpu_opt_time_microseconds, self.t_sent, self.time_start))
        buf.write(struct.pack('>8d', *self.dt_pred[:8]))
        for i0 in range(8):
            buf.write(struct.pack('>12d', *self.x_opt[i0][:12]))
        for i0 in range(8):
            buf.write(struct.pack('>24d', *self.u_opt[i0][:24]))
        for i0 in range(3):
            buf.write(struct.pack('>4d', *self.p_opt[i0][:4]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != rpc_data_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return rpc_data_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = rpc_data_t()
        self.cpu_opt_time_microseconds, self.t_sent, self.time_start = struct.unpack(">ddd", buf.read(24))
        self.dt_pred = struct.unpack('>8d', buf.read(64))
        self.x_opt = []
        for i0 in range(8):
            self.x_opt.append(struct.unpack('>12d', buf.read(96)))
        self.u_opt = []
        for i0 in range(8):
            self.u_opt.append(struct.unpack('>24d', buf.read(192)))
        self.p_opt = []
        for i0 in range(3):
            self.p_opt.append(struct.unpack('>4d', buf.read(32)))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if rpc_data_t in parents: return 0
        tmphash = (0xf933cc03f4843206) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if rpc_data_t._packed_fingerprint is None:
            rpc_data_t._packed_fingerprint = struct.pack(">Q", rpc_data_t._get_hash_recursive([]))
        return rpc_data_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

