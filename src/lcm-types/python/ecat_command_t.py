"""LCM type definitions
This file automatically generated by lcm.
DO NOT MODIFY BY HAND!!!!
"""

try:
    import cStringIO.StringIO as BytesIO
except ImportError:
    from io import BytesIO
import struct

class ecat_command_t(object):
    __slots__ = ["x_des", "y_des", "z_des", "dx_des", "dy_des", "dz_des", "kpx", "kpy", "kpz", "kdx", "kdy", "kdz", "enable", "zero_joints", "fx_ff", "fy_ff", "fz_ff", "tau_abad_ff", "tau_hip_ff", "tau_knee_ff", "abad_zero_angle", "hip_zero_angle", "knee_zero_angle", "q_des_abad", "q_des_hip", "q_des_knee", "qd_des_abad", "qd_des_hip", "qd_des_knee", "kp_joint_abad", "kp_joint_hip", "kp_joint_knee", "kd_joint_abad", "kd_joint_hip", "kd_joint_knee", "max_torque"]

    __typenames__ = ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "int32_t", "int32_t", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float"]

    __dimensions__ = [[4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4], [4]]

    def __init__(self):
        self.x_des = [ 0.0 for dim0 in range(4) ]
        self.y_des = [ 0.0 for dim0 in range(4) ]
        self.z_des = [ 0.0 for dim0 in range(4) ]
        self.dx_des = [ 0.0 for dim0 in range(4) ]
        self.dy_des = [ 0.0 for dim0 in range(4) ]
        self.dz_des = [ 0.0 for dim0 in range(4) ]
        self.kpx = [ 0.0 for dim0 in range(4) ]
        self.kpy = [ 0.0 for dim0 in range(4) ]
        self.kpz = [ 0.0 for dim0 in range(4) ]
        self.kdx = [ 0.0 for dim0 in range(4) ]
        self.kdy = [ 0.0 for dim0 in range(4) ]
        self.kdz = [ 0.0 for dim0 in range(4) ]
        self.enable = [ 0 for dim0 in range(4) ]
        self.zero_joints = [ 0 for dim0 in range(4) ]
        self.fx_ff = [ 0.0 for dim0 in range(4) ]
        self.fy_ff = [ 0.0 for dim0 in range(4) ]
        self.fz_ff = [ 0.0 for dim0 in range(4) ]
        self.tau_abad_ff = [ 0.0 for dim0 in range(4) ]
        self.tau_hip_ff = [ 0.0 for dim0 in range(4) ]
        self.tau_knee_ff = [ 0.0 for dim0 in range(4) ]
        self.abad_zero_angle = [ 0.0 for dim0 in range(4) ]
        self.hip_zero_angle = [ 0.0 for dim0 in range(4) ]
        self.knee_zero_angle = [ 0.0 for dim0 in range(4) ]
        self.q_des_abad = [ 0.0 for dim0 in range(4) ]
        self.q_des_hip = [ 0.0 for dim0 in range(4) ]
        self.q_des_knee = [ 0.0 for dim0 in range(4) ]
        self.qd_des_abad = [ 0.0 for dim0 in range(4) ]
        self.qd_des_hip = [ 0.0 for dim0 in range(4) ]
        self.qd_des_knee = [ 0.0 for dim0 in range(4) ]
        self.kp_joint_abad = [ 0.0 for dim0 in range(4) ]
        self.kp_joint_hip = [ 0.0 for dim0 in range(4) ]
        self.kp_joint_knee = [ 0.0 for dim0 in range(4) ]
        self.kd_joint_abad = [ 0.0 for dim0 in range(4) ]
        self.kd_joint_hip = [ 0.0 for dim0 in range(4) ]
        self.kd_joint_knee = [ 0.0 for dim0 in range(4) ]
        self.max_torque = [ 0.0 for dim0 in range(4) ]

    def encode(self):
        buf = BytesIO()
        buf.write(ecat_command_t._get_packed_fingerprint())
        self._encode_one(buf)
        return buf.getvalue()

    def _encode_one(self, buf):
        buf.write(struct.pack('>4f', *self.x_des[:4]))
        buf.write(struct.pack('>4f', *self.y_des[:4]))
        buf.write(struct.pack('>4f', *self.z_des[:4]))
        buf.write(struct.pack('>4f', *self.dx_des[:4]))
        buf.write(struct.pack('>4f', *self.dy_des[:4]))
        buf.write(struct.pack('>4f', *self.dz_des[:4]))
        buf.write(struct.pack('>4f', *self.kpx[:4]))
        buf.write(struct.pack('>4f', *self.kpy[:4]))
        buf.write(struct.pack('>4f', *self.kpz[:4]))
        buf.write(struct.pack('>4f', *self.kdx[:4]))
        buf.write(struct.pack('>4f', *self.kdy[:4]))
        buf.write(struct.pack('>4f', *self.kdz[:4]))
        buf.write(struct.pack('>4i', *self.enable[:4]))
        buf.write(struct.pack('>4i', *self.zero_joints[:4]))
        buf.write(struct.pack('>4f', *self.fx_ff[:4]))
        buf.write(struct.pack('>4f', *self.fy_ff[:4]))
        buf.write(struct.pack('>4f', *self.fz_ff[:4]))
        buf.write(struct.pack('>4f', *self.tau_abad_ff[:4]))
        buf.write(struct.pack('>4f', *self.tau_hip_ff[:4]))
        buf.write(struct.pack('>4f', *self.tau_knee_ff[:4]))
        buf.write(struct.pack('>4f', *self.abad_zero_angle[:4]))
        buf.write(struct.pack('>4f', *self.hip_zero_angle[:4]))
        buf.write(struct.pack('>4f', *self.knee_zero_angle[:4]))
        buf.write(struct.pack('>4f', *self.q_des_abad[:4]))
        buf.write(struct.pack('>4f', *self.q_des_hip[:4]))
        buf.write(struct.pack('>4f', *self.q_des_knee[:4]))
        buf.write(struct.pack('>4f', *self.qd_des_abad[:4]))
        buf.write(struct.pack('>4f', *self.qd_des_hip[:4]))
        buf.write(struct.pack('>4f', *self.qd_des_knee[:4]))
        buf.write(struct.pack('>4f', *self.kp_joint_abad[:4]))
        buf.write(struct.pack('>4f', *self.kp_joint_hip[:4]))
        buf.write(struct.pack('>4f', *self.kp_joint_knee[:4]))
        buf.write(struct.pack('>4f', *self.kd_joint_abad[:4]))
        buf.write(struct.pack('>4f', *self.kd_joint_hip[:4]))
        buf.write(struct.pack('>4f', *self.kd_joint_knee[:4]))
        buf.write(struct.pack('>4f', *self.max_torque[:4]))

    def decode(data):
        if hasattr(data, 'read'):
            buf = data
        else:
            buf = BytesIO(data)
        if buf.read(8) != ecat_command_t._get_packed_fingerprint():
            raise ValueError("Decode error")
        return ecat_command_t._decode_one(buf)
    decode = staticmethod(decode)

    def _decode_one(buf):
        self = ecat_command_t()
        self.x_des = struct.unpack('>4f', buf.read(16))
        self.y_des = struct.unpack('>4f', buf.read(16))
        self.z_des = struct.unpack('>4f', buf.read(16))
        self.dx_des = struct.unpack('>4f', buf.read(16))
        self.dy_des = struct.unpack('>4f', buf.read(16))
        self.dz_des = struct.unpack('>4f', buf.read(16))
        self.kpx = struct.unpack('>4f', buf.read(16))
        self.kpy = struct.unpack('>4f', buf.read(16))
        self.kpz = struct.unpack('>4f', buf.read(16))
        self.kdx = struct.unpack('>4f', buf.read(16))
        self.kdy = struct.unpack('>4f', buf.read(16))
        self.kdz = struct.unpack('>4f', buf.read(16))
        self.enable = struct.unpack('>4i', buf.read(16))
        self.zero_joints = struct.unpack('>4i', buf.read(16))
        self.fx_ff = struct.unpack('>4f', buf.read(16))
        self.fy_ff = struct.unpack('>4f', buf.read(16))
        self.fz_ff = struct.unpack('>4f', buf.read(16))
        self.tau_abad_ff = struct.unpack('>4f', buf.read(16))
        self.tau_hip_ff = struct.unpack('>4f', buf.read(16))
        self.tau_knee_ff = struct.unpack('>4f', buf.read(16))
        self.abad_zero_angle = struct.unpack('>4f', buf.read(16))
        self.hip_zero_angle = struct.unpack('>4f', buf.read(16))
        self.knee_zero_angle = struct.unpack('>4f', buf.read(16))
        self.q_des_abad = struct.unpack('>4f', buf.read(16))
        self.q_des_hip = struct.unpack('>4f', buf.read(16))
        self.q_des_knee = struct.unpack('>4f', buf.read(16))
        self.qd_des_abad = struct.unpack('>4f', buf.read(16))
        self.qd_des_hip = struct.unpack('>4f', buf.read(16))
        self.qd_des_knee = struct.unpack('>4f', buf.read(16))
        self.kp_joint_abad = struct.unpack('>4f', buf.read(16))
        self.kp_joint_hip = struct.unpack('>4f', buf.read(16))
        self.kp_joint_knee = struct.unpack('>4f', buf.read(16))
        self.kd_joint_abad = struct.unpack('>4f', buf.read(16))
        self.kd_joint_hip = struct.unpack('>4f', buf.read(16))
        self.kd_joint_knee = struct.unpack('>4f', buf.read(16))
        self.max_torque = struct.unpack('>4f', buf.read(16))
        return self
    _decode_one = staticmethod(_decode_one)

    _hash = None
    def _get_hash_recursive(parents):
        if ecat_command_t in parents: return 0
        tmphash = (0xad03831ee9458f34) & 0xffffffffffffffff
        tmphash  = (((tmphash<<1)&0xffffffffffffffff) + (tmphash>>63)) & 0xffffffffffffffff
        return tmphash
    _get_hash_recursive = staticmethod(_get_hash_recursive)
    _packed_fingerprint = None

    def _get_packed_fingerprint():
        if ecat_command_t._packed_fingerprint is None:
            ecat_command_t._packed_fingerprint = struct.pack(">Q", ecat_command_t._get_hash_recursive([]))
        return ecat_command_t._packed_fingerprint
    _get_packed_fingerprint = staticmethod(_get_packed_fingerprint)

