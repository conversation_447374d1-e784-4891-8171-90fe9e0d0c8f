/*
 * MIT Control - DisturbanceEstimator 数据记录使用示例
 * 
 * 该文件展示了如何在代码中启用DisturbanceEstimator的数据记录功能
 * 
 * 作者: MIT Control Team
 * 日期: 2025-09-26
 */

#include "Controllers/DisturbanceEst/DisturbanceEstimator.h"
#include <iostream>
#include <thread>
#include <chrono>

void example_usage() {
    std::cout << "=== DisturbanceEstimator 数据记录示例 ===" << std::endl;
    
    // 1. 创建DisturbanceEstimator实例
    float dt = 0.002f;  // 2ms控制周期
    int iterations_between_mpc = 1;
    MIT_UserParameters* params = nullptr;  // 在实际使用中需要提供有效的参数
    
    DisturbanceEstimator estimator(dt, iterations_between_mpc, params);
    estimator.initialize();
    
    // 2. 启用数据记录
    std::cout << "启用数据记录..." << std::endl;
    estimator.enableDataLogging(true);  // 使用默认文件名
    // 或者指定文件名:
    // estimator.enableDataLogging(true, "my_disturbance_data.csv");
    
    // 3. 模拟运行一段时间（在实际应用中，这会在控制循环中调用）
    std::cout << "模拟运行 5 秒..." << std::endl;
    auto start_time = std::chrono::steady_clock::now();
    int iteration = 0;
    
    while (true) {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            current_time - start_time).count();
        
        if (elapsed > 5000) break;  // 运行5秒
        
        // 在实际应用中，这里会调用 estimator.run(data) 和 estimator.update(data)
        // 这里我们只是模拟数据记录的调用
        if (iteration % 10 == 0) {  // 每20ms记录一次
            // 模拟一些数据变化
            estimator.mp_fused = 5.0f + 0.5f * sin(elapsed * 0.001f);
            estimator.mp_torque_based = 4.8f + 0.3f * cos(elapsed * 0.0015f);
            estimator.mp = 5.2f + 0.2f * sin(elapsed * 0.0008f);
            estimator.torque_confidence = 0.8f + 0.1f * sin(elapsed * 0.002f);
            
            // 记录数据（在实际应用中，这会在run()函数中自动调用）
            estimator.logData();
        }
        
        iteration++;
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
    
    // 4. 停止数据记录
    std::cout << "停止数据记录..." << std::endl;
    estimator.enableDataLogging(false);
    
    // 5. 可选：导出数据到另一个文件
    std::cout << "导出数据到自定义文件..." << std::endl;
    estimator.saveDataToCSV("exported_disturbance_data.csv");
    
    std::cout << "示例完成！" << std::endl;
    std::cout << "\n使用以下命令查看数据:" << std::endl;
    std::cout << "python3 plot_disturbance_estimator.py --file exported_disturbance_data.csv" << std::endl;
}

/*
 * 在实际的控制器代码中的集成示例:
 * 
 * 在FSM状态或控制器初始化时:
 * ```cpp
 * // 在某个FSM状态的onEnter()中
 * void FSM_State_Locomotion::onEnter() {
 *     // ... 其他初始化代码 ...
 *     
 *     // 启用扰动估计器数据记录
 *     if (this->_data->userParameters->enable_disturbance_logging) {
 *         std::string filename = "locomotion_disturbance_" + 
 *                               getCurrentTimeString() + ".csv";
 *         this->_data->_disturbanceEstimator->enableDataLogging(true, filename);
 *     }
 * }
 * 
 * // 在控制循环中 (run()函数)
 * void FSM_State_Locomotion::run() {
 *     // ... 控制逻辑 ...
 *     
 *     // 运行扰动估计器 (数据记录会自动进行)
 *     this->_data->_disturbanceEstimator->run(*this->_data);
 *     this->_data->_disturbanceEstimator->update(*this->_data);
 *     
 *     // ... 其他控制代码 ...
 * }
 * 
 * // 在状态退出时
 * void FSM_State_Locomotion::onExit() {
 *     // 停止数据记录
 *     this->_data->_disturbanceEstimator->enableDataLogging(false);
 *     
 *     // ... 其他清理代码 ...
 * }
 * ```
 */

/*
 * 用户参数配置示例:
 * 
 * 可以在MIT_UserParameters中添加以下参数来控制数据记录:
 * 
 * ```cpp
 * // 在MIT_UserParameters.h中添加:
 * DECLARE_PARAMETER(bool, enable_disturbance_logging);
 * DECLARE_PARAMETER(std::string, disturbance_log_filename);
 * DECLARE_PARAMETER(int, disturbance_log_frequency);  // 记录频率 (每N次控制循环记录一次)
 * 
 * // 在配置文件中设置:
 * enable_disturbance_logging: true
 * disturbance_log_filename: "auto"  # "auto"表示自动生成文件名
 * disturbance_log_frequency: 1      # 每次都记录
 * ```
 */

int main() {
    example_usage();
    return 0;
}
