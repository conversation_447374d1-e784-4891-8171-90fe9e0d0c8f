# DisturbanceEstimator 数据记录与可视化

本文档介绍了如何使用DisturbanceEstimator的数据记录功能来保存和可视化`mp_fused`、`mp_torque_based`等估计数据。

## 功能概述

DisturbanceEstimator现在支持以下数据记录功能：

### 记录的数据字段
- `timestamp`: 时间戳 (秒)
- `mp_fused`: 融合后的负载质量估计 (kg)
- `mp_torque_based`: 基于扭矩的负载质量估计 (kg)
- `mp`: 基于力传感器的负载质量估计 (kg)
- `torque_confidence`: 扭矩路径置信度 (0-1)
- `total_contact_force`: 总接触力 (N)
- `acc_world_x/y/z`: 世界系加速度 (m/s²)
- `disturbance_x/y/z`: 扰动估计
- `d_est_x/y/z`: 扰动状态估计

## 使用方法

### 1. 在代码中启用数据记录

```cpp
#include "Controllers/DisturbanceEst/DisturbanceEstimator.h"

// 创建估计器实例
DisturbanceEstimator estimator(dt, iterations_between_mpc, parameters);
estimator.initialize();

// 启用数据记录 (自动生成文件名)
estimator.enableDataLogging(true);

// 或者指定文件名
estimator.enableDataLogging(true, "my_experiment_data.csv");

// 在控制循环中运行 (数据会自动记录)
estimator.run(data);
estimator.update(data);

// 停止记录
estimator.enableDataLogging(false);

// 可选：导出数据到另一个文件
estimator.saveDataToCSV("exported_data.csv");
```

### 2. 数据可视化

使用提供的Python脚本来可视化数据：

```bash
# 从CSV文件绘制图表
python3 plot_disturbance_estimator.py --file your_data.csv

# 生成演示数据并绘图
python3 plot_disturbance_estimator.py --demo

# 只显示统计信息，不绘图
python3 plot_disturbance_estimator.py --file your_data.csv --stats-only

# 不保存图片文件
python3 plot_disturbance_estimator.py --file your_data.csv --no-save
```

### 3. 图表说明

绘图脚本会生成包含6个子图的综合分析图表：

1. **质量估计对比**: 显示`mp_fused`、`mp_torque_based`和`mp`的时间序列
2. **估计误差**: 显示各估计方法相对于参考值的误差
3. **置信度与接触力**: 双Y轴图显示扭矩路径置信度和总接触力
4. **世界系加速度**: 显示X、Y、Z三个方向的加速度
5. **扰动估计**: 显示扰动估计的三个分量
6. **扰动状态估计**: 显示d_est的三个分量

## 实际应用集成

### 在FSM状态中集成

```cpp
// 在FSM状态的onEnter()中启用记录
void FSM_State_Locomotion::onEnter() {
    if (this->_data->userParameters->enable_disturbance_logging) {
        std::string filename = "locomotion_disturbance_" + 
                              getCurrentTimeString() + ".csv";
        this->_data->_disturbanceEstimator->enableDataLogging(true, filename);
    }
}

// 在run()中正常运行 (数据自动记录)
void FSM_State_Locomotion::run() {
    this->_data->_disturbanceEstimator->run(*this->_data);
    this->_data->_disturbanceEstimator->update(*this->_data);
}

// 在onExit()中停止记录
void FSM_State_Locomotion::onExit() {
    this->_data->_disturbanceEstimator->enableDataLogging(false);
}
```

### 用户参数配置

可以在用户参数中添加以下配置：

```yaml
# 在配置文件中
enable_disturbance_logging: true
disturbance_log_filename: "auto"  # "auto"表示自动生成文件名
disturbance_log_frequency: 1      # 每次都记录 (1), 或每N次记录一次
```

## 文件说明

### 新增文件
- `plot_disturbance_estimator.py`: 数据可视化脚本
- `enable_disturbance_logging_example.cpp`: 使用示例代码
- `DISTURBANCE_ESTIMATOR_LOGGING_README.md`: 本说明文档

### 修改的文件
- `src/user/MIT_Controller/Controllers/DisturbanceEst/DisturbanceEstimator.h`: 添加了数据记录相关的成员变量和函数声明
- `src/user/MIT_Controller/Controllers/DisturbanceEst/DisturbanceEstimator.cpp`: 实现了数据记录功能

## 性能考虑

- 数据记录每100次写入会自动刷新文件缓冲区
- 数据同时存储在内存向量中，便于后续分析
- 可以通过调整记录频率来平衡数据完整性和性能

## 故障排除

### 常见问题

1. **文件无法创建**: 检查文件路径权限和磁盘空间
2. **数据为空**: 确保在调用`run()`和`update()`之前启用了数据记录
3. **Python脚本报错**: 确保安装了必要的依赖包：
   ```bash
   pip3 install matplotlib pandas numpy
   ```

### 调试技巧

- 启用调试输出查看数据记录状态：
  ```cpp
  estimator.enablePIDDebug(true);
  ```
- 检查生成的CSV文件是否包含预期的数据
- 使用`--demo`选项测试绘图脚本是否正常工作

## 扩展功能

### 添加新的记录字段

1. 在`DisturbanceEstimator.h`中添加新的向量成员
2. 在`logData()`函数中添加数据记录逻辑
3. 在`saveDataToCSV()`函数中添加CSV输出
4. 更新绘图脚本以可视化新字段

### 实时监控

可以扩展现有的`realtime_mp_monitor.py`脚本来支持更多字段的实时监控。

## 示例输出

运行演示数据生成：
```bash
python3 plot_disturbance_estimator.py --demo
```

将生成包含以下统计信息的输出：
```
============================================================
DisturbanceEstimator 数据统计分析
============================================================
数据点数: 15000
时间跨度: 30.00 秒
采样频率: 500.0 Hz

质量估计统计:
  mp_fused     - 均值: 5.002 kg, 标准差: 0.425 kg
  mp_torque    - 均值: 4.998 kg, 标准差: 0.387 kg
  mp (参考)    - 均值: 5.001 kg, 标准差: 0.632 kg

估计误差统计:
  mp_fused 误差     - MAE: 0.3124 kg, RMSE: 0.4156 kg
  mp_torque 误差    - MAE: 0.2987 kg, RMSE: 0.3892 kg

置信度统计:
  平均置信度: 0.800
  置信度范围: [0.651, 0.949]

接触力统计:
  平均接触力: 137.2 N
  接触力范围: [98.4, 176.8] N
```

## 联系信息

如有问题或建议，请联系MIT Control Team。
