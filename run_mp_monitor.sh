#!/bin/bash

# MIT Control - MP 监控启动脚本
# 
# 该脚本用于启动 mp_fused 和 mp_real 的对比监控
# 
# 使用方法:
#   ./run_mp_monitor.sh [选项]
#
# 选项:
#   realtime    - 实时监控模式
#   demo        - 演示模式 (生成示例数据)
#   help        - 显示帮助信息
#
# 作者: MIT Control Team
# 日期: 2025-09-26

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$SCRIPT_DIR/build"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "MIT Control - MP 监控启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  realtime    启动实时监控模式"
    echo "  demo        运行演示模式 (生成示例数据)"
    echo "  help        显示此帮助信息"
    echo ""
    echo "实时监控模式说明:"
    echo "  1. 确保控制器编译完成"
    echo "  2. 脚本会自动启动控制器并启用调试输出"
    echo "  3. 同时启动实时绘图工具"
    echo "  4. 按 Ctrl+C 退出监控"
    echo ""
    echo "演示模式说明:"
    echo "  生成示例数据并显示对比图表，用于测试绘图功能"
    echo ""
    echo "示例:"
    echo "  $0 realtime    # 启动实时监控"
    echo "  $0 demo        # 运行演示"
    echo ""
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查 matplotlib
    if ! python3 -c "import matplotlib" &> /dev/null; then
        print_error "matplotlib 未安装，请运行: pip3 install matplotlib"
        exit 1
    fi
    
    # 检查 numpy
    if ! python3 -c "import numpy" &> /dev/null; then
        print_error "numpy 未安装，请运行: pip3 install numpy"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查控制器编译状态
check_controller() {
    if [ ! -f "$BUILD_DIR/mit_ctrl" ]; then
        print_error "控制器未编译，请先运行编译"
        print_info "编译命令: cd build && make -j$(nproc)"
        exit 1
    fi
    
    print_success "控制器已编译"
}

# 启动实时监控
start_realtime_monitoring() {
    print_info "启动实时监控模式..."
    
    check_dependencies
    check_controller
    
    # 检查绘图脚本
    if [ ! -f "$SCRIPT_DIR/realtime_mp_monitor.py" ]; then
        print_error "实时监控脚本不存在: realtime_mp_monitor.py"
        exit 1
    fi
    
    print_info "准备启动控制器和监控工具..."
    print_warning "确保在启动前设置了正确的控制器参数"
    print_info "控制器将以仿真模式启动 (Mini Cheetah)"
    
    # 询问用户确认
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "用户取消操作"
        exit 0
    fi
    
    print_info "启动监控工具..."
    
    # 设置环境变量启用调试输出
    export ENABLE_PID_DEBUG=1
    
    # 启动控制器并通过管道传递给监控工具
    cd "$BUILD_DIR"
    print_info "启动控制器: ./mit_ctrl m s"
    print_info "监控工具将在新窗口中显示图表"
    print_warning "按 Ctrl+C 停止监控"
    
    # 使用管道连接控制器和监控工具
    ./mit_ctrl m s | python3 "$SCRIPT_DIR/realtime_mp_monitor.py"
}

# 运行演示模式
run_demo() {
    print_info "启动演示模式..."
    
    check_dependencies
    
    # 检查绘图脚本
    if [ ! -f "$SCRIPT_DIR/plot_mp_comparison.py" ]; then
        print_error "绘图脚本不存在: plot_mp_comparison.py"
        exit 1
    fi
    
    print_info "生成示例数据并显示对比图表..."
    
    # 运行演示
    python3 "$SCRIPT_DIR/plot_mp_comparison.py"
    
    print_success "演示完成"
}

# 主函数
main() {
    case "${1:-}" in
        "realtime")
            start_realtime_monitoring
            ;;
        "demo")
            run_demo
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        "")
            print_warning "未指定选项"
            show_help
            exit 1
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
print_info "MIT Control - MP 监控启动脚本"
print_info "脚本位置: $SCRIPT_DIR"

main "$@"
